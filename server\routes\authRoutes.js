const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { logOperation } = require('../middlewares/logger');
const { verifyUserToken } = require('../middlewares/auth');

// 验证卡密
router.post(
  '/verify',
  logOperation('卡密验证', '用户验证卡密'),
  authController.verifyCardKey
);

// 检查令牌有效性和重用状态
router.get(
  '/check',
  verifyUserToken,
  logOperation('令牌检查', '检查令牌有效性和重用状态'),
  authController.checkToken
);

// 管理员登录
router.post(
  '/admin/login',
  logOperation('管理员登录', '管理员账户登录'),
  authController.adminLogin
);

module.exports = router; 