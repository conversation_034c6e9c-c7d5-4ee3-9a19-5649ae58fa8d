const { <PERSON><PERSON>ey } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 清理30天前过期或使用过的卡密
 * 功能：删除满足以下条件的卡密：
 * 1. 已被使用且使用时间超过30天
 * 2. 已过期且过期时间超过30天
 * 
 * @returns {Promise<Object>} 包含清理结果的对象
 */
const cleanupExpiredCardKeys = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    // 计算30天前的日期
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // 查找需要删除的卡密
    const cardsToDelete = await CardKey.findAll({
      where: {
        [Op.or]: [
          // 条件1: 已使用且使用时间超过30天前
          {
            is_used: true,
            used_time: {
              [Op.not]: null,
              [Op.lt]: thirtyDaysAgo
            }
          },
          // 条件2: 已过期且过期时间超过30天前
          {
            expire_time: {
              [Op.not]: null,
              [Op.lt]: thirtyDaysAgo
            }
          }
        ]
      },
      attributes: ['id'],
      transaction
    });
    
    // 获取要删除卡密的ID列表
    const cardIdsToDelete = cardsToDelete.map(card => card.id);
    
    // 如果没有需要删除的卡密，直接返回
    if (cardIdsToDelete.length === 0) {
      await transaction.commit();
      return { success: true, message: '没有需要清理的卡密', deletedCount: 0 };
    }
    
    // 先清除外键关联，解除与文件的关联
    await CardKey.update(
      { used_file_id: null },
      { 
        where: { id: { [Op.in]: cardIdsToDelete } },
        transaction
      }
    );
    
    // 删除过期卡密
    const deletedCount = await CardKey.destroy({
      where: {
        id: {
          [Op.in]: cardIdsToDelete
        }
      },
      transaction
    });
    
    // 提交事务
    await transaction.commit();
    
    return {
      success: true,
      message: `成功清理 ${deletedCount} 个过期或已使用卡密`,
      deletedCount
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('清理过期卡密错误:', error);
    return {
      success: false,
      message: '清理过期卡密时发生错误',
      error: error.message
    };
  }
};

/**
 * 清理所有已使用的卡密
 * 功能：删除所有已使用的卡密，不考虑使用时间
 * 
 * @returns {Promise<Object>} 包含清理结果的对象
 */
const cleanupUsedCardKeys = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    // 查找所有已使用的卡密
    const cardsToDelete = await CardKey.findAll({
      where: {
        is_used: true
      },
      attributes: ['id'],
      transaction
    });
    
    // 获取要删除卡密的ID列表
    const cardIdsToDelete = cardsToDelete.map(card => card.id);
    
    // 如果没有需要删除的卡密，直接返回
    if (cardIdsToDelete.length === 0) {
      await transaction.commit();
      return { success: true, message: '没有已使用的卡密需要清理', deletedCount: 0 };
    }
    
    // 先清除外键关联，解除与文件的关联
    await CardKey.update(
      { used_file_id: null },
      { 
        where: { id: { [Op.in]: cardIdsToDelete } },
        transaction
      }
    );
    
    // 删除已使用的卡密
    const deletedCount = await CardKey.destroy({
      where: {
        id: {
          [Op.in]: cardIdsToDelete
        }
      },
      transaction
    });
    
    // 提交事务
    await transaction.commit();
    
    return {
      success: true,
      message: `成功清理 ${deletedCount} 个已使用的卡密`,
      deletedCount
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('清理已使用卡密错误:', error);
    return {
      success: false,
      message: '清理已使用卡密时发生错误',
      error: error.message
    };
  }
};

/**
 * 清理所有无效卡密
 * 功能：删除满足以下任一条件的卡密：
 * 1. 已被使用（不限时间）
 * 2. 已过期（不限时间）
 * 3. 状态为无效的卡密
 * 
 * @returns {Promise<Object>} 包含清理结果的对象
 */
const cleanupInvalidCardKeys = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    // 当前时间
    const now = new Date();
    
    // 查找需要删除的卡密
    const cardsToDelete = await CardKey.findAll({
      where: {
        [Op.or]: [
          // 条件1: 所有已使用的卡密
          {
            is_used: true
          },
          // 条件2: 所有已过期的卡密
          {
            expire_time: {
              [Op.not]: null,
              [Op.lt]: now
            }
          },
          // 条件3: 状态为无效的卡密
          {
            status: false
          }
        ]
      },
      attributes: ['id'],
      transaction
    });
    
    // 获取要删除卡密的ID列表
    const cardIdsToDelete = cardsToDelete.map(card => card.id);
    
    // 如果没有需要删除的卡密，直接返回
    if (cardIdsToDelete.length === 0) {
      await transaction.commit();
      return { success: true, message: '没有无效卡密需要清理', deletedCount: 0 };
    }
    
    // 先清除外键关联，解除与文件的关联
    await CardKey.update(
      { used_file_id: null },
      { 
        where: { id: { [Op.in]: cardIdsToDelete } },
        transaction
      }
    );
    
    // 删除无效卡密
    const deletedCount = await CardKey.destroy({
      where: {
        id: {
          [Op.in]: cardIdsToDelete
        }
      },
      transaction
    });
    
    // 提交事务
    await transaction.commit();
    
    return {
      success: true,
      message: `成功清理 ${deletedCount} 个无效卡密`,
      deletedCount
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('清理无效卡密错误:', error);
    return {
      success: false,
      message: '清理无效卡密时发生错误',
      error: error.message
    };
  }
};

module.exports = {
  cleanupExpiredCardKeys,
  cleanupUsedCardKeys,
  cleanupInvalidCardKeys
}; 