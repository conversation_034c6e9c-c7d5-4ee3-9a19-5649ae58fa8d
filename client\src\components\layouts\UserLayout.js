import React from 'react';
import { Layout, Menu, Button, Space, Dropdown } from 'antd';
import { UserOutlined, LogoutOutlined, FileOutlined, MenuFoldOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { logout } from '../../store/slices/authSlice';

const { Header, Content, Footer } = Layout;

const UserLayout = ({ children }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
  };

  const dropdownItems = [
    {
      key: '1',
      label: '文件列表',
      icon: <FileOutlined />,
      onClick: () => navigate('/files')
    },
    {
      key: '2',
      label: '返回首页',
      icon: <LogoutOutlined />,
      onClick: handleLogout
    }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        position: 'fixed',
        zIndex: 1000,
        width: '100%',
        top: 0
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/">
            <h1 style={{ color: '#fff', margin: 0, fontSize: '30px', marginRight: '20px', paddingLeft: '5px' }}>
              自助提取系统
            </h1>
          </Link>
        </div>
        
        <div style={{ position: 'absolute', right: '3px' }}>
          <Space>
            <Dropdown menu={{ items: dropdownItems }} placement="bottomRight">
              <Button type="text" icon={<UserOutlined />} style={{ color: '#fff' }}>
                <MenuFoldOutlined />
              </Button>
            </Dropdown>
          </Space>
        </div>
      </Header>

      <Content style={{ 
        padding: '24px', 
        backgroundColor: '#fff',
        marginTop: 64 // 为固定导航栏腾出空间
      }}>
        <div className="container">{children}</div>
      </Content>

      <Footer style={{ textAlign: 'center', padding: '12px' }}>
        自助提取系统 ©{new Date().getFullYear()} 版权所有
      </Footer>
    </Layout>
  );
};

export default UserLayout; 