const { Setting } = require('../models');

/**
 * 获取所有设置
 */
const getSettings = async (req, res) => {
  try {
    const settings = await Setting.findAll({
      order: [['setting_key', 'ASC']]
    });
    
    // 处理加密字段，不返回敏感信息的完整值
    const processedSettings = settings.map(setting => {
      const settingData = setting.toJSON();
      
      if (setting.is_encrypted && settingData.setting_value) {
        // 对于加密字段，只显示前几位和后几位
        const value = settingData.setting_value;
        if (value.length > 10) {
          settingData.setting_value = value.substring(0, 4) + '****' + value.substring(value.length - 4);
        } else if (value.length > 0) {
          settingData.setting_value = '****';
        }
        settingData.has_value = value.length > 0;
      }
      
      return settingData;
    });
    
    res.json({
      success: true,
      data: processedSettings
    });
  } catch (error) {
    console.error('获取设置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取设置失败'
    });
  }
};

/**
 * 获取单个设置
 */
const getSetting = async (req, res) => {
  try {
    const { key } = req.params;
    const value = await Setting.getValue(key);
    
    res.json({
      success: true,
      data: {
        key,
        value
      }
    });
  } catch (error) {
    console.error('获取设置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取设置失败'
    });
  }
};

/**
 * 更新设置
 */
const updateSetting = async (req, res) => {
  try {
    const { key } = req.params;
    const { value, type, description } = req.body;
    
    await Setting.setValue(key, value, type, description);
    
    res.json({
      success: true,
      message: '设置更新成功'
    });
  } catch (error) {
    console.error('更新设置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新设置失败'
    });
  }
};

/**
 * 批量更新设置
 */
const updateSettings = async (req, res) => {
  try {
    const { settings } = req.body;
    
    for (const setting of settings) {
      await Setting.setValue(setting.key, setting.value, setting.type, setting.description);
    }
    
    res.json({
      success: true,
      message: '设置更新成功'
    });
  } catch (error) {
    console.error('批量更新设置失败:', error);
    res.status(500).json({
      success: false,
      message: '批量更新设置失败'
    });
  }
};

/**
 * 获取闲鱼设置
 */
const getXianyuSettings = async (req, res) => {
  try {
    const cookies = await Setting.getXianyuCookies();
    const maxItems = await Setting.getXianyuMaxItems();
    
    res.json({
      success: true,
      data: {
        cookies: cookies ? (cookies.length > 10 ? cookies.substring(0, 4) + '****' + cookies.substring(cookies.length - 4) : '****') : '',
        has_cookies: cookies.length > 0,
        max_items: maxItems
      }
    });
  } catch (error) {
    console.error('获取闲鱼设置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取闲鱼设置失败'
    });
  }
};

/**
 * 更新闲鱼设置
 */
const updateXianyuSettings = async (req, res) => {
  try {
    const { cookies, max_items } = req.body;
    
    if (cookies !== undefined) {
      await Setting.setXianyuCookies(cookies);
    }
    
    if (max_items !== undefined) {
      await Setting.setXianyuMaxItems(max_items);
    }
    
    res.json({
      success: true,
      message: '闲鱼设置更新成功'
    });
  } catch (error) {
    console.error('更新闲鱼设置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新闲鱼设置失败'
    });
  }
};

module.exports = {
  getSettings,
  getSetting,
  updateSetting,
  updateSettings,
  getXianyuSettings,
  updateXianyuSettings
};
