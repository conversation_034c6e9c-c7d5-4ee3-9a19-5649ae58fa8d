import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  Spin,
  Typography,
  Tag,
  Space,
  Button
} from 'antd';
import {
  DownloadOutlined,
  FileOutlined,
  EyeOutlined,
  TrophyOutlined,
  Bar<PERSON><PERSON>Outlined,
  Pie<PERSON>hartOutlined,
  Line<PERSON>hartOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { getSystemStats, getPopularFiles, getDetailedAnalytics } from '../../store/slices/adminSlice';
import ReactECharts from 'echarts-for-react';

const { Title, Text } = Typography;
const { Option } = Select;

const DataAnalytics = () => {
  const dispatch = useDispatch();
  const { 
    stats, 
    loadingStats, 
    popularFiles, 
    loadingPopularFiles, 
    detailedAnalytics, 
    loadingDetailedAnalytics 
  } = useSelector((state) => state.admin);

  const [timeRange, setTimeRange] = useState(30); // 默认30天
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, [dispatch, timeRange]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadData = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        dispatch(getSystemStats()),
        dispatch(getPopularFiles({ limit: 10, days: timeRange })),
        dispatch(getDetailedAnalytics({ days: timeRange }))
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadData();
  };

  const handleTimeRangeChange = (value) => {
    setTimeRange(value);
  };

  // 提取排行表格列配置
  const extractionRankColumns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      render: (_, __, index) => {
        const rank = index + 1;
        if (rank <= 3) {
          const colors = ['#FFD700', '#C0C0C0', '#CD7F32'];
          return <TrophyOutlined style={{ color: colors[rank - 1], fontSize: 16 }} />;
        }
        return rank;
      }
    },
    {
      title: '文件名',
      dataIndex: ['file', 'file_name'],
      key: 'file_name',
      ellipsis: true,
      render: (text) => <Text ellipsis title={text}>{text}</Text>
    },
    {
      title: '分类',
      dataIndex: ['file', 'Category'],
      key: 'category',
      width: 300,
      align: 'left',
      render: (category) => category ? <Tag color="green">{category.name}</Tag> : '-'
    },
    {
      title: '提取次数',
      dataIndex: 'extraction_count',
      key: 'extraction_count',
      width: 150,
      render: (count) => <Statistic value={count} valueStyle={{ fontSize: 14, color: '#52c41a' }} />
    }
  ];

  // 生成分类统计饼图配置
  const getCategoryPieOption = () => {
    if (!detailedAnalytics?.categoryStats) return {};

    const data = detailedAnalytics.categoryStats.map(item => ({
      name: item.name,
      value: parseInt(item.file_count)
    }));

    return {
      title: {
        text: '分类文件分布',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '文件数量',
          type: 'pie',
          radius: '50%',
          data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  };

  // 生成文件类型柱状图配置
  const getFileTypeBarOption = () => {
    if (!detailedAnalytics?.fileTypeStats) return {};

    const data = detailedAnalytics.fileTypeStats;
    const xData = data.map(item => item.file_type);
    const yData = data.map(item => parseInt(item.count));

    // 定义多种颜色用于区分不同文件类型
    const colors = [
      '#1890ff', // 蓝色
      '#52c41a', // 绿色
      '#fa8c16', // 橙色
      '#722ed1', // 紫色
      '#eb2f96', // 粉色
      '#13c2c2', // 青色
      '#faad14', // 黄色
      '#f5222d', // 红色
      '#2f54eb', // 深蓝色
      '#a0d911'  // 浅绿色
    ];

    // 为每个数据项分配颜色
    const coloredData = yData.map((value, index) => ({
      value: value,
      itemStyle: {
        color: colors[index % colors.length]
      }
    }));

    return {
      title: {
        text: '文件类型统计',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: { rotate: 45 }
      },
      yAxis: {
        type: 'value',
        name: '文件数量'
      },
      series: [
        {
          name: '文件数量',
          type: 'bar',
          data: coloredData
        }
      ]
    };
  };

  // 生成月度趋势线图配置
  const getMonthlyTrendOption = () => {
    if (!detailedAnalytics?.monthlyStats) return {};

    const data = detailedAnalytics.monthlyStats;
    const xData = data.map(item => item.month);
    const yData = data.map(item => parseInt(item.extraction_count));

    return {
      title: {
        text: '月度提取趋势',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: xData,
        name: '月份'
      },
      yAxis: {
        type: 'value',
        name: '提取次数'
      },
      series: [
        {
          name: '提取次数',
          type: 'line',
          data: yData,
          smooth: true,
          itemStyle: {
            color: '#52c41a'
          },
          areaStyle: {
            color: 'rgba(82, 196, 26, 0.2)'
          }
        }
      ]
    };
  };

  // 生成24小时分布图配置
  const getHourlyDistributionOption = () => {
    if (!detailedAnalytics?.hourlyStats) return {};

    // 创建24小时的完整数据数组
    const hourlyData = new Array(24).fill(0);
    detailedAnalytics.hourlyStats.forEach(item => {
      hourlyData[parseInt(item.hour)] = parseInt(item.count);
    });

    const xData = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    // 定义时间段和对应颜色
    const getTimeSegmentColor = (hour) => {
      if (hour >= 0 && hour < 6) return '#2f54eb';   // 凌晨 (0-5) - 深蓝色
      if (hour >= 6 && hour < 12) return '#52c41a';  // 早上 (6-11) - 绿色
      if (hour >= 12 && hour < 14) return '#fa8c16'; // 中午 (12-13) - 橙色
      if (hour >= 14 && hour < 18) return '#1890ff'; // 下午 (14-17) - 蓝色
      if (hour >= 18 && hour < 24) return '#722ed1'; // 晚上 (18-23) - 紫色
      return '#722ed1';
    };

    // 为每个小时的数据分配颜色
    const coloredData = hourlyData.map((value, hour) => ({
      value: value,
      itemStyle: {
        color: getTimeSegmentColor(hour)
      }
    }));

    return {
      title: {
        text: '24小时提取分布',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const hour = params[0].dataIndex;
          let timeSegment = '';
          if (hour >= 0 && hour < 6) timeSegment = '凌晨';
          else if (hour >= 6 && hour < 12) timeSegment = '早上';
          else if (hour >= 12 && hour < 14) timeSegment = '中午';
          else if (hour >= 14 && hour < 18) timeSegment = '下午';
          else if (hour >= 18 && hour < 24) timeSegment = '晚上';

          return `${params[0].name} (${timeSegment}): ${params[0].value} 次提取`;
        }
      },
      legend: {
        data: ['凌晨 (0-5)', '早上 (6-11)', '中午 (12-13)', '下午 (14-17)', '晚上 (18-23)'],
        bottom: 10
      },
      xAxis: {
        type: 'category',
        data: xData,
        name: '时间'
      },
      yAxis: {
        type: 'value',
        name: '提取次数'
      },
      series: [
        {
          name: '提取次数',
          type: 'bar',
          data: coloredData
        }
      ]
    };
  };

  // 生成日提取趋势线图配置
  const getDailyTrendOption = () => {
    if (!detailedAnalytics?.dailyStats) {
      console.log('dailyStats 数据不存在:', detailedAnalytics);
      return {};
    }

    const data = detailedAnalytics.dailyStats;
    console.log('dailyStats 数据:', data);
    const xData = data.map(item => item.date);
    const yData = data.map(item => parseInt(item.extraction_count));

    return {
      title: {
        text: '日提取趋势',
        left: 'center',
        textStyle: { fontSize: 16 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c} 次提取'
      },
      xAxis: {
        type: 'category',
        data: xData,
        name: '日期',
        axisLabel: {
          rotate: 45,
          formatter: function(value) {
            // 格式化日期显示，只显示月-日
            return value.substring(5);
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '提取次数'
      },
      series: [
        {
          name: '提取次数',
          type: 'line',
          data: yData,
          smooth: true,
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: 'rgba(24, 144, 255, 0.2)'
          }
        }
      ]
    };
  };

  return (
    <div>
      {/* 页面标题和控制栏 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            <BarChartOutlined /> 数据分析
          </Title>
        </Col>
        <Col>
          <Space>
            <Select
              value={timeRange}
              onChange={handleTimeRangeChange}
              style={{ width: 120 }}
            >
              <Option value={7}>近7天</Option>
              <Option value={30}>近30天</Option>
              <Option value={90}>近90天</Option>
              <Option value={365}>近一年</Option>
            </Select>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={refreshing}
            >
              刷新数据
            </Button>
          </Space>
        </Col>
      </Row>

      <Spin spinning={loadingStats || loadingPopularFiles || loadingDetailedAnalytics || refreshing}>
        {/* 数据概览卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="文件总数"
                value={stats?.fileStats?.total || 0}
                prefix={<FileOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总提取次数"
                value={stats?.totalExtractions || 0}
                prefix={<DownloadOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="今日提取"
                value={stats?.todayExtractions || 0}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="近7日提取量"
                value={stats?.last7DaysExtractions || 0}
                prefix={<DownloadOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 图表分析区域 */}
        {/* 日提取趋势图表 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24}>
            <Card title={<><LineChartOutlined /> 日提取趋势</>}>
              <Spin spinning={loadingDetailedAnalytics}>
                <ReactECharts
                  option={getDailyTrendOption()}
                  style={{ height: '300px' }}
                />
              </Spin>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card title={<><LineChartOutlined /> 月度提取趋势</>}>
              <Spin spinning={loadingDetailedAnalytics}>
                <ReactECharts
                  option={getMonthlyTrendOption()}
                  style={{ height: '300px' }}
                />
              </Spin>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="24小时提取分布">
              <Spin spinning={loadingDetailedAnalytics}>
                <ReactECharts
                  option={getHourlyDistributionOption()}
                  style={{ height: '300px' }}
                />
              </Spin>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card title={<><PieChartOutlined /> 分类分布</>}>
              <Spin spinning={loadingDetailedAnalytics}>
                <ReactECharts
                  option={getCategoryPieOption()}
                  style={{ height: '300px' }}
                />
              </Spin>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title={<><BarChartOutlined /> 文件类型统计</>}>
              <Spin spinning={loadingDetailedAnalytics}>
                <ReactECharts
                  option={getFileTypeBarOption()}
                  style={{ height: '300px' }}
                />
              </Spin>
            </Card>
          </Col>
        </Row>

        {/* 热门文件排行榜 */}
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card
              title={<><TrophyOutlined /> 热门文件排行榜（按提取次数）</>}
              extra={<Text type="secondary">近{timeRange}天</Text>}
            >
              <Spin spinning={loadingPopularFiles}>
                <Table
                  columns={extractionRankColumns}
                  dataSource={popularFiles?.popularByExtractions?.map((item, index) => ({
                    ...item,
                    key: item.file_id,
                    rank: index + 1
                  })) || []}
                  pagination={false}
                  size="small"
                  scroll={{ x: 600 }}
                />
              </Spin>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default DataAnalytics;
