@echo off
setlocal EnableDelayedExpansion

echo ==================================================
echo          ZiZhu TiHuo System Launcher v1.0
echo ==================================================
echo.

:: Save current directory path
set "PROJECT_ROOT=%CD%"
echo Project Root: %PROJECT_ROOT%

echo Checking environment...

:: Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not detected! Please install Node.js before running this script.
    echo You can download and install Node.js from https://nodejs.org
    pause
    exit /b 1
)

echo [SUCCESS] Node.js is installed

:: Check if project folder structure is correct
if not exist "server" (
    echo [ERROR] Server directory not found! Please make sure to run this script in the correct project root directory.
    pause
    exit /b 1
)

if not exist "client" (
    echo [ERROR] Client directory not found! Please make sure to run this script in the correct project root directory.
    pause
    exit /b 1
)

echo [SUCCESS] Project file structure verification passed

:: Verify file integrity
echo.
echo Verifying file integrity...
node "%PROJECT_ROOT%\server\utils\verifyIntegrity.js"
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] File integrity verification failed!
    echo System detected that critical files have been modified and cannot start.
    echo Please restore original files and try again.
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] File integrity verification passed

:: Set window title
title ZiZhu TiHuo System Launcher

echo.
echo Checking dependencies...

:: Check if dependencies need to be installed
set "NEED_SERVER_INSTALL=0"
set "NEED_CLIENT_INSTALL=0"

echo Checking backend dependencies...
if not exist "%PROJECT_ROOT%\server\node_modules" (
    echo [INFO] Backend dependencies not installed
    set "NEED_SERVER_INSTALL=1"
) else (
    echo [SUCCESS] Backend dependencies already exist
)

echo Checking frontend dependencies...
if not exist "%PROJECT_ROOT%\client\node_modules" (
    echo [INFO] Frontend dependencies not installed
    set "NEED_CLIENT_INSTALL=1"
) else (
    echo [SUCCESS] Frontend dependencies already exist
)

:: If both dependencies exist, skip installation
if !NEED_SERVER_INSTALL! equ 0 if !NEED_CLIENT_INSTALL! equ 0 (
    echo [SUCCESS] All dependencies already exist
    goto :start_services
)

:: Create installation scripts for parallel execution
echo.
echo Creating parallel installation processes...

:: Create server installation script
if !NEED_SERVER_INSTALL! equ 1 (
    echo @echo off > "%PROJECT_ROOT%\install-server.bat"
    echo title Installing Server Dependencies >> "%PROJECT_ROOT%\install-server.bat"
    echo echo ================================================== >> "%PROJECT_ROOT%\install-server.bat"
    echo echo          Installing Server Dependencies >> "%PROJECT_ROOT%\install-server.bat"
    echo echo ================================================== >> "%PROJECT_ROOT%\install-server.bat"
    echo cd /d "%PROJECT_ROOT%\server" >> "%PROJECT_ROOT%\install-server.bat"
    echo echo Attempting to install dependencies with fallback options... >> "%PROJECT_ROOT%\install-server.bat"
    echo npm install --no-optional --legacy-peer-deps >> "%PROJECT_ROOT%\install-server.bat"
    echo if %%errorlevel%% neq 0 ^( >> "%PROJECT_ROOT%\install-server.bat"
    echo     echo [WARNING] Standard install failed, trying with force flag... >> "%PROJECT_ROOT%\install-server.bat"
    echo     npm install --force --no-optional >> "%PROJECT_ROOT%\install-server.bat"
    echo ^) >> "%PROJECT_ROOT%\install-server.bat"
    echo if %%errorlevel%% neq 0 ^( >> "%PROJECT_ROOT%\install-server.bat"
    echo     echo [ERROR] Server dependencies installation failed! >> "%PROJECT_ROOT%\install-server.bat"
    echo     echo @echo off ^> "%PROJECT_ROOT%\server-install-error.bat" >> "%PROJECT_ROOT%\install-server.bat"
    echo     echo echo Server installation failed! ^>^> "%PROJECT_ROOT%\server-install-error.bat" >> "%PROJECT_ROOT%\install-server.bat"
    echo     echo Press any key to close this window... >> "%PROJECT_ROOT%\install-server.bat"
    echo     pause ^> nul >> "%PROJECT_ROOT%\install-server.bat"
    echo     exit /b 1 >> "%PROJECT_ROOT%\install-server.bat"
    echo ^) >> "%PROJECT_ROOT%\install-server.bat"
    echo echo [SUCCESS] Server dependencies installation completed >> "%PROJECT_ROOT%\install-server.bat"
    echo echo @echo off ^> "%PROJECT_ROOT%\server-install-success.bat" >> "%PROJECT_ROOT%\install-server.bat"
    echo echo echo Server installation completed successfully! ^>^> "%PROJECT_ROOT%\server-install-success.bat" >> "%PROJECT_ROOT%\install-server.bat"
    echo echo Installation completed successfully! This window will close in 3 seconds... >> "%PROJECT_ROOT%\install-server.bat"
    echo timeout /t 3 /nobreak ^> nul >> "%PROJECT_ROOT%\install-server.bat"
    echo exit /b 0 >> "%PROJECT_ROOT%\install-server.bat"
)

:: Create client installation script
if !NEED_CLIENT_INSTALL! equ 1 (
    echo @echo off > "%PROJECT_ROOT%\install-client.bat"
    echo title Installing Client Dependencies >> "%PROJECT_ROOT%\install-client.bat"
    echo echo ================================================== >> "%PROJECT_ROOT%\install-client.bat"
    echo echo          Installing Client Dependencies >> "%PROJECT_ROOT%\install-client.bat"
    echo echo ================================================== >> "%PROJECT_ROOT%\install-client.bat"
    echo cd /d "%PROJECT_ROOT%\client" >> "%PROJECT_ROOT%\install-client.bat"
    echo echo Attempting to install dependencies with fallback options... >> "%PROJECT_ROOT%\install-client.bat"
    echo npm install --no-optional --legacy-peer-deps >> "%PROJECT_ROOT%\install-client.bat"
    echo if %%errorlevel%% neq 0 ^( >> "%PROJECT_ROOT%\install-client.bat"
    echo     echo [WARNING] Standard install failed, trying with force flag... >> "%PROJECT_ROOT%\install-client.bat"
    echo     npm install --force --no-optional >> "%PROJECT_ROOT%\install-client.bat"
    echo ^) >> "%PROJECT_ROOT%\install-client.bat"
    echo if %%errorlevel%% neq 0 ^( >> "%PROJECT_ROOT%\install-client.bat"
    echo     echo [ERROR] Client dependencies installation failed! >> "%PROJECT_ROOT%\install-client.bat"
    echo     echo @echo off ^> "%PROJECT_ROOT%\client-install-error.bat" >> "%PROJECT_ROOT%\install-client.bat"
    echo     echo echo Client installation failed! ^>^> "%PROJECT_ROOT%\client-install-error.bat" >> "%PROJECT_ROOT%\install-client.bat"
    echo     echo Press any key to close this window... >> "%PROJECT_ROOT%\install-client.bat"
    echo     pause ^> nul >> "%PROJECT_ROOT%\install-client.bat"
    echo     exit /b 1 >> "%PROJECT_ROOT%\install-client.bat"
    echo ^) >> "%PROJECT_ROOT%\install-client.bat"
    echo echo [SUCCESS] Client dependencies installation completed >> "%PROJECT_ROOT%\install-client.bat"
    echo echo @echo off ^> "%PROJECT_ROOT%\client-install-success.bat" >> "%PROJECT_ROOT%\install-client.bat"
    echo echo echo Client installation completed successfully! ^>^> "%PROJECT_ROOT%\client-install-success.bat" >> "%PROJECT_ROOT%\install-client.bat"
    echo echo Installation completed successfully! This window will close in 3 seconds... >> "%PROJECT_ROOT%\install-client.bat"
    echo timeout /t 3 /nobreak ^> nul >> "%PROJECT_ROOT%\install-client.bat"
    echo exit /b 0 >> "%PROJECT_ROOT%\install-client.bat"
)

:: Clean up any existing flag files
if exist "%PROJECT_ROOT%\server-install-success.bat" del "%PROJECT_ROOT%\server-install-success.bat"
if exist "%PROJECT_ROOT%\server-install-error.bat" del "%PROJECT_ROOT%\server-install-error.bat"
if exist "%PROJECT_ROOT%\client-install-success.bat" del "%PROJECT_ROOT%\client-install-success.bat"
if exist "%PROJECT_ROOT%\client-install-error.bat" del "%PROJECT_ROOT%\client-install-error.bat"

:: Start parallel installation processes
echo.
echo Starting parallel dependency installation...
echo Please wait for both installation processes to complete...
echo.

if !NEED_SERVER_INSTALL! equ 1 (
    echo Starting server dependencies installation in new window...
    start "Server Dependencies Installation" "%PROJECT_ROOT%\install-server.bat"
)

if !NEED_CLIENT_INSTALL! equ 1 (
    echo Starting client dependencies installation in new window...
    start "Client Dependencies Installation" "%PROJECT_ROOT%\install-client.bat"
)

:: Wait for installations to complete
echo.
echo Waiting for installations to complete...
echo (You can monitor progress in the opened terminal windows)
echo.

:: Initialize timeout counter (max 10 minutes = 300 seconds)
set "WAIT_COUNTER=0"
set "MAX_WAIT=300"
set "LAST_STATUS_TIME=0"

:wait_loop
set /a WAIT_COUNTER+=2
set /a STATUS_INTERVAL=!WAIT_COUNTER! %% 10
timeout /t 2 /nobreak > nul

:: Check if server installation is needed and completed
set "SERVER_DONE=1"
if !NEED_SERVER_INSTALL! equ 1 (
    if not exist "%PROJECT_ROOT%\server-install-success.bat" (
        if not exist "%PROJECT_ROOT%\server-install-error.bat" (
            set "SERVER_DONE=0"
        )
    )
)

:: Check if client installation is needed and completed
set "CLIENT_DONE=1"
if !NEED_CLIENT_INSTALL! equ 1 (
    if not exist "%PROJECT_ROOT%\client-install-success.bat" (
        if not exist "%PROJECT_ROOT%\client-install-error.bat" (
            set "CLIENT_DONE=0"
        )
    )
)

:: Show progress every 10 seconds
if !STATUS_INTERVAL! equ 0 (
    echo [INFO] Still waiting... (!WAIT_COUNTER!/!MAX_WAIT! seconds elapsed)
    if !NEED_SERVER_INSTALL! equ 1 (
        if !SERVER_DONE! equ 0 (
            echo   - Server dependencies: Installing...
        ) else (
            echo   - Server dependencies: Completed
        )
    )
    if !NEED_CLIENT_INSTALL! equ 1 (
        if !CLIENT_DONE! equ 0 (
            echo   - Client dependencies: Installing...
        ) else (
            echo   - Client dependencies: Completed
        )
    )
)

:: Check for timeout
if !WAIT_COUNTER! geq !MAX_WAIT! (
    echo.
    echo [ERROR] Installation timeout! Installations are taking too long.
    echo Please check the installation windows for any errors or prompts.
    echo You can also try running the installations manually:
    echo   - For server: cd server ^&^& npm install
    echo   - For client: cd client ^&^& npm install
    pause
    exit /b 1
)

:: Continue waiting if installations are not complete
if !SERVER_DONE! equ 0 goto :wait_loop
if !CLIENT_DONE! equ 0 goto :wait_loop

:: Check for installation errors
if exist "%PROJECT_ROOT%\server-install-error.bat" (
    echo [ERROR] Server dependencies installation failed!
    echo You can check the error details by running: server-install-error.bat
    pause
    exit /b 1
)

if exist "%PROJECT_ROOT%\client-install-error.bat" (
    echo [ERROR] Client dependencies installation failed!
    echo You can check the error details by running: client-install-error.bat
    pause
    exit /b 1
)

echo.
echo ==================================================
echo [SUCCESS] All dependency installations completed!
echo ==================================================
echo.
echo Cleaning up temporary files...

:: Clean up temporary files
if exist "%PROJECT_ROOT%\install-server.bat" del "%PROJECT_ROOT%\install-server.bat"
if exist "%PROJECT_ROOT%\install-client.bat" del "%PROJECT_ROOT%\install-client.bat"
if exist "%PROJECT_ROOT%\server-install-success.bat" del "%PROJECT_ROOT%\server-install-success.bat"
if exist "%PROJECT_ROOT%\client-install-success.bat" del "%PROJECT_ROOT%\client-install-success.bat"

echo [SUCCESS] Cleanup completed
echo.
echo Now continuing with system startup in main terminal...

:start_services
echo.
echo ==================================================
echo          Starting ZiZhu TiHuo System
echo ==================================================
echo.

:: Return to project root directory
cd /d "%PROJECT_ROOT%"

echo Starting backend service...
echo.

:: Start backend service, backend service will verify authorization
cd /d "%PROJECT_ROOT%\server"
start "ZiZhu TiHuo System - Backend Service" cmd /c "npm start"

:: Wait a few seconds for backend to start and complete authorization verification
echo Waiting for backend service to start...
echo Please enter authorization code in the popup console window...
timeout /t 8 /nobreak > nul

:: Check if backend started successfully
:: Use PowerShell instead of curl to check if API service is responding
powershell -Command "try { $null = Invoke-WebRequest -Uri 'http://localhost:5000/api' -UseBasicParsing -ErrorAction Stop; exit 0 } catch { exit 1 }"
if %errorlevel% neq 0 (
    echo [WARNING] Backend service may not have started successfully, please check if authorization was successful.
    echo.
    echo Waiting additional time before retry...
    timeout /t 5 /nobreak > nul
    
    :: Try checking backend again
    powershell -Command "try { $null = Invoke-WebRequest -Uri 'http://localhost:5000/api' -UseBasicParsing -ErrorAction Stop; exit 0 } catch { exit 1 }"
    if %errorlevel% neq 0 (
        echo [ERROR] Backend service startup failed, please check if authorization passed.
        pause
        exit /b 1
    )
)

echo [SUCCESS] Backend service started

:: Start frontend development server
echo.
echo Starting frontend service...

:: Create a temporary startup script to ensure execution in correct directory
echo @echo off > "%PROJECT_ROOT%\start-frontend.bat"
echo cd /d "%PROJECT_ROOT%\client" >> "%PROJECT_ROOT%\start-frontend.bat"
echo echo Starting frontend service... >> "%PROJECT_ROOT%\start-frontend.bat"
echo npm start >> "%PROJECT_ROOT%\start-frontend.bat"
echo pause >> "%PROJECT_ROOT%\start-frontend.bat"

:: Start frontend service
start "ZiZhu TiHuo System - Frontend Service" "%PROJECT_ROOT%\start-frontend.bat"

:: Return to project root directory
cd /d "%PROJECT_ROOT%"

echo.
echo ==================================================
echo      System started! Please visit the following addresses in your browser:
echo      Frontend Interface: http://localhost:3000
echo      Backend API: http://localhost:5000/api
echo ==================================================
echo.
echo Note: Closing this window will not stop the services, you need to manually close the service windows

:: Keep script running
pause
exit /b 0
