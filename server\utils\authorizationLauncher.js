const readline = require('readline');
const { isLocallyAuthorized, verifyAndBindAuthCode } = require('./directAuthorization');

/**
 * 请求用户输入授权码
 * @returns {Promise<string>} 用户输入的授权码
 */
function promptForAuthCode() {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('请输入您的授权码以激活系统: ', (authCode) => {
      rl.close();
      resolve(authCode.trim());
    });
  });
}

/**
 * 验证系统授权
 * @returns {Promise<boolean>} 是否授权成功
 */
async function verifySystemAuthorization() {
  try {
    console.log('正在检查系统授权状态...');
    
    // 检查本地是否已授权
    const isAuthorized = await isLocallyAuthorized();
    
    if (isAuthorized) {
      console.log('授权验证成功！系统启动中...');
      return true;
    }
    
    // 未授权，请求输入授权码
    console.log('需要授权验证才能启动系统');
    
    let authSuccess = false;
    let attempts = 0;
    const maxAttempts = 3;
    
    while (!authSuccess && attempts < maxAttempts) {
      const authCode = await promptForAuthCode();
      
      console.log('正在验证授权码...');
      const result = await verifyAndBindAuthCode(authCode);
      
      if (result.success) {
        console.log('授权成功！系统启动中...');
        authSuccess = true;
      } else {
        attempts++;
        console.log(`${result.message}${attempts < maxAttempts ? '，请重试' : ''}`);
      }
    }
    
    if (!authSuccess) {
      console.log('授权验证失败，系统将退出');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('授权验证过程发生错误:', error);
    return false;
  }
}

module.exports = {
  verifySystemAuthorization
}; 