import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Alert, Typography, Layout } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { adminLogin, clearAdminError } from '../../store/slices/adminSlice';

const { Title } = Typography;
const { Header, Content, Footer } = Layout;

const AdminLogin = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { loading, error, isAuthenticated } = useSelector((state) => state.admin);

  // 如果已登录，重定向到管理后台首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/admin');
    }
    
    return () => {
      dispatch(clearAdminError());
    };
  }, [isAuthenticated, navigate, dispatch]);

  const handleSubmit = (values) => {
    dispatch(adminLogin(values));
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        position: 'fixed',
        zIndex: 1000,
        width: '100%',
        top: 0
      }}>
        <Title level={4} style={{ color: '#fff', margin: 0 }}>
          自助提取系统 · 管理后台
        </Title>
      </Header>

      <Content style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        padding: '50px 20px',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        marginTop: 64 // 为固定导航栏腾出空间
      }}>
        <Card
          title={
            <div style={{ textAlign: 'center' }}>
              <Title level={3}>管理员登录</Title>
            </div>
          }
          variant="borderless"
          style={{ width: 400, boxShadow: '0 4px 12px rgba(0,0,0,0.1)', borderRadius: 8 }}
        >
          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              style={{ marginBottom: 24 }}
              onClose={() => dispatch(clearAdminError())}
            />
          )}

          <Form
            form={form}
            name="admin_login"
            onFinish={handleSubmit}
            layout="vertical"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入管理员账号' }]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="管理员账号" 
                size="large" 
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入管理员密码' }]}
            >
              <Input.Password 
                prefix={<LockOutlined />} 
                placeholder="管理员密码" 
                size="large" 
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                block
                loading={loading}
              >
                登录管理后台
              </Button>
            </Form.Item>

            <div style={{ textAlign: 'center' }}>
              <Link to="/">返回前台首页</Link>
            </div>
          </Form>
        </Card>
      </Content>

      <Footer style={{ textAlign: 'center' }}>
        自助提取系统 ©{new Date().getFullYear()} 管理后台
      </Footer>
    </Layout>
  );
};

export default AdminLogin; 