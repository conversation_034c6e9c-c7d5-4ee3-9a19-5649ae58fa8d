const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 存储文件哈希值的对象
const fileHashes = {
  // 授权验证相关文件
  'server/controllers/authController.js': '',
  'server/controllers/cardKeyController.js': '',
  'server/controllers/fileController.js': '',
  'server/models/CardKey.js': '',
  'server/utils/jwt.js': '',
  'server/utils/directAuthorization.js': '',
  'server/utils/authorizationLauncher.js': '',
  'server/middlewares/auth.js': '',
  'server/middlewares/logger.js': '',
  'server/routes/authRoutes.js': '',
  'server/utils/keyGenerator.js': '',
  'client/src/store/slices/authSlice.js': '',
};

/**
 * 计算文件的哈希值
 * @param {String} filePath - 文件路径
 * @returns {String} - 文件哈希值
 */
function calculateFileHash(filePath) {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    const hashSum = crypto.createHash('sha256');
    hashSum.update(fileBuffer);
    return hashSum.digest('hex');
  } catch (error) {
    console.error(`无法计算文件哈希值: ${filePath}`, error);
    throw new Error(`无法计算文件哈希值: ${filePath}`);
  }
}

/**
 * 初始化文件哈希值
 * @param {String} basePath - 项目根目录的路径
 * @returns {Object} - 文件路径和对应的哈希值
 */
function initializeFileHashes(basePath) {
  console.log('初始化文件哈希值...');
  const hashes = {};
  
  for (const filePath of Object.keys(fileHashes)) {
    const absolutePath = path.join(basePath, filePath);
    try {
      const hash = calculateFileHash(absolutePath);
      hashes[filePath] = hash;
      console.log(`已计算 ${filePath} 的哈希值: ${hash}`);
    } catch (error) {
      console.error(`初始化哈希值失败: ${filePath}`, error);
      process.exit(1); // 关键文件损坏，退出进程
    }
  }
  
  return hashes;
}

/**
 * 验证文件完整性
 * @param {String} basePath - 项目根目录的路径
 * @param {Object} storedHashes - 存储的文件哈希值
 * @returns {Boolean} - 是否通过完整性验证
 */
function validateFileIntegrity(basePath, storedHashes) {
  console.log('验证文件完整性...');
  let allValid = true;
  
  for (const [filePath, storedHash] of Object.entries(storedHashes)) {
    const absolutePath = path.join(basePath, filePath);
    try {
      const currentHash = calculateFileHash(absolutePath);
      const isValid = currentHash === storedHash;
      
      if (!isValid) {
        console.error(`文件完整性验证失败: ${filePath}`);
        console.error(`预期哈希值: ${storedHash}`);
        console.error(`实际哈希值: ${currentHash}`);
        allValid = false;
      }
    } catch (error) {
      console.error(`验证文件完整性失败: ${filePath}`, error);
      allValid = false;
    }
  }
  
  return allValid;
}

module.exports = {
  calculateFileHash,
  initializeFileHashes,
  validateFileIntegrity,
  fileHashes
}; 