import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  message,
  Typography,
  Spin,
  Popconfirm,
  Tag,
  App
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FileOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { getAdminCategories } from '../../store/slices/adminSlice';
import axios from 'axios';

const { Title, Text } = Typography;
const { TextArea } = Input;

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const Categories = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [form] = Form.useForm();
  const { message: messageApi } = App.useApp();

  const dispatch = useDispatch();
  const { adminCategories, loadingAdminCategories } = useSelector((state) => state.admin);
  
  // 加载分类列表
  useEffect(() => {
    dispatch(getAdminCategories());
  }, [dispatch]);

  // 处理新增或编辑分类
  const handleSaveCategory = async (values) => {
    try {
      // 获取管理员token
      const token = localStorage.getItem('adminToken');
      if (!token) {
        messageApi.error('会话已过期，请重新登录');
        return;
      }
      
      // 设置请求头
      const headers = {
        'Authorization': `Bearer ${token}`
      };
      
      if (editingCategory) {
        // 编辑现有分类
        await axios.patch(`/api/admin/categories/${editingCategory.id}`, values, { headers });
        messageApi.success(`分类"${values.name}"更新成功`);
      } else {
        // 添加新分类
        await axios.post('/api/admin/categories', values, { headers });
        messageApi.success(`分类"${values.name}"创建成功`);
      }

      setModalVisible(false);
      form.resetFields();
      setEditingCategory(null);
      
      // 重新加载分类列表
      dispatch(getAdminCategories());
    } catch (error) {
      messageApi.error(error.response?.data?.message || '操作失败，请重试');
    }
  };

  // 打开编辑模态框
  const showEditModal = (category) => {
    setEditingCategory(category);
    form.setFieldsValue({
      name: category.name,
      description: category.description
    });
    setModalVisible(true);
  };

  // 打开添加模态框
  const showAddModal = () => {
    setEditingCategory(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理删除分类
  const handleDeleteCategory = async (id) => {
    try {
      // 获取管理员token
      const token = localStorage.getItem('adminToken');
      if (!token) {
        messageApi.error('会话已过期，请重新登录');
        return;
      }
      
      // 设置请求头
      const headers = {
        'Authorization': `Bearer ${token}`
      };
      
      // 删除分类
      await axios.delete(`/api/admin/categories/${id}`, { headers });
      messageApi.success('分类删除成功');
      
      // 重新加载分类列表
      dispatch(getAdminCategories());
    } catch (error) {
      messageApi.error(error.response?.data?.message || '删除失败，请重试');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '文件数量',
      key: 'fileCount',
      dataIndex: 'fileCount',
      render: (_, record) => (
        <Tag color="blue" icon={<FileOutlined />}>
          {record.Files?.length || 0} 个文件
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (text) => formatDate(text)
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          
          <Popconfirm
            title="确定删除此分类?"
            description="删除分类后，关联的文件将变为未分类状态"
            onConfirm={() => handleDeleteCategory(record.id)}
          >
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Title level={2}>分类管理</Title>
      <Text type="secondary" style={{ marginBottom: 20, display: 'block' }}>
        管理系统中的文件分类，便于用户快速找到所需资料。
      </Text>

      <Card>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          style={{ marginBottom: 16 }}
          onClick={showAddModal}
        >
          添加分类
        </Button>

        <Spin spinning={loadingAdminCategories}>
          <Table
            columns={columns}
            dataSource={adminCategories.map(category => ({ ...category, key: category.id }))}
            pagination={{ pageSize: 10 }}
          />
        </Spin>
      </Card>

      {/* 添加/编辑分类的模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCategory(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveCategory}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              {editingCategory ? '保存' : '添加'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Categories; 