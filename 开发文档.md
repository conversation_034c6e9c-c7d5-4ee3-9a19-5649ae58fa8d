# 自助提取系统开发文档

## 项目概述
自助提取系统是一个基于Web的应用程序，允许用户通过输入有效的卡密来获取系统中的文件资料。每个卡密一次只能提取一个文件资料，系统支持自动生成卡密功能，同时提供管理界面以便管理员上传和管理文件资料及卡密。

## 功能需求

### 用户端功能
1. **登录验证**
   - 用户输入卡密进行验证
   - 验证卡密有效性（是否存在、是否已使用）
   - 验证通过后显示可提取的文件资料列表

2. **文件资料浏览**
   - 以列表形式展示所有可提取的文件资料
   - 提供文件分类和搜索功能
   - 显示文件基本信息（名称、类型、简介等）

3. **文件资料详情查看**
   - 点击文件后显示详细信息（详细描述、上传日期、文件大小等）
   - 确认提取功能
   - 提取后标记卡密为已使用状态

4. **文件资料下载/查看**
   - 提供文件下载链接或在线查看功能
   - 支持主流文件格式

### 管理员功能
1. **卡密管理**
   - 自动生成卡密
   - 批量导出卡密
   - 查看卡密使用状态
   - 卡密作废、续期等操作

2. **文件资料管理**
   - 上传文件资料
   - 编辑文件资料信息
   - 删除或下架文件资料
   - 文件资料分类管理

3. **系统统计**
   - 卡密使用情况统计
   - 文件提取情况统计
   - 用户访问日志

## 技术架构

### 前端技术栈
- **框架**：React.js
- **UI库**：Ant Design
- **状态管理**：Redux
- **路由**：React Router
- **请求处理**：Axios
- **构建工具**：Webpack

### 后端技术栈
- **框架**：Node.js + Express
- **数据库**：MySQL 8.0
- **ORM**：Sequelize
- **身份验证**：JWT
- **文件存储**：本地文件系统或云存储(可选)

### 数据库设计

#### 表结构

1. **卡密表(card_keys)**
   ```sql
   CREATE TABLE card_keys (
       id INT AUTO_INCREMENT PRIMARY KEY,
       key_code VARCHAR(50) NOT NULL UNIQUE COMMENT '卡密码',
       is_used TINYINT(1) DEFAULT 0 COMMENT '是否已使用',
       used_time DATETIME NULL COMMENT '使用时间',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       expire_time DATETIME NULL COMMENT '过期时间',
       status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效,0:无效)',
       used_file_id INT NULL COMMENT '已提取的文件ID',
       UNIQUE KEY (key_code)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

2. **文件资料表(files)**
   ```sql
   CREATE TABLE files (
       id INT AUTO_INCREMENT PRIMARY KEY,
       file_name VARCHAR(255) NOT NULL COMMENT '文件名称',
       file_path VARCHAR(255) NOT NULL COMMENT '文件路径',
       file_size INT NOT NULL COMMENT '文件大小(KB)',
       file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
       description TEXT COMMENT '文件描述',
       upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
       download_count INT DEFAULT 0 COMMENT '下载次数',
       status TINYINT(1) DEFAULT 1 COMMENT '状态(1:可用,0:不可用)',
       category_id INT NULL COMMENT '分类ID'
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

3. **分类表(categories)**
   ```sql
   CREATE TABLE categories (
       id INT AUTO_INCREMENT PRIMARY KEY,
       name VARCHAR(100) NOT NULL COMMENT '分类名称',
       description VARCHAR(255) NULL COMMENT '分类描述',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

4. **管理员表(admins)**
   ```sql
   CREATE TABLE admins (
       id INT AUTO_INCREMENT PRIMARY KEY,
       username VARCHAR(50) NOT NULL COMMENT '用户名',
       password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
       last_login DATETIME NULL COMMENT '最后登录时间',
       status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

5. **操作日志表(operation_logs)**
   ```sql
   CREATE TABLE operation_logs (
       id INT AUTO_INCREMENT PRIMARY KEY,
       operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
       operation_desc VARCHAR(255) NOT NULL COMMENT '操作描述',
       operator_ip VARCHAR(50) NULL COMMENT '操作IP',
       card_key VARCHAR(50) NULL COMMENT '相关卡密',
       file_id INT NULL COMMENT '相关文件ID',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

## 系统流程

### 用户提取流程
1. 用户访问系统首页
2. 输入卡密进行验证
3. 验证成功后，展示可提取的文件资料列表
4. 用户浏览并选择需要的文件资料
5. 点击文件资料，查看详细信息
6. 确认提取，系统标记卡密为已使用
7. 用户获取文件资料(下载或查看)

### 卡密生成流程
1. 管理员登录后台
2. 进入卡密管理页面
3. 设置生成数量和参数
4. 系统自动生成指定数量的卡密
5. 管理员可导出卡密或查看卡密列表

## 接口设计

### 用户接口

1. **卡密验证**
   - 路径: `/api/verify`
   - 方法: POST
   - 参数: `{key_code: "卡密字符串"}`
   - 返回: `{success: true, message: "验证成功", data: {token: "JWT令牌"}}`

2. **获取文件列表**
   - 路径: `/api/files`
   - 方法: GET
   - 参数: 可选(分类ID、搜索关键词等)
   - 返回: `{success: true, data: {files: [...文件列表]}}`

3. **获取文件详情**
   - 路径: `/api/files/:id`
   - 方法: GET
   - 返回: `{success: true, data: {file: {文件详情}}}`

4. **确认提取文件**
   - 路径: `/api/extract/:fileId`
   - 方法: POST
   - 返回: `{success: true, message: "提取成功", data: {downloadUrl: "下载链接"}}`

5. **文件下载**
   - 路径: `/api/download/:fileId`
   - 方法: GET
   - 返回: 文件流

### 管理员接口

1. **管理员登录**
   - 路径: `/api/admin/login`
   - 方法: POST
   - 参数: `{username: "用户名", password: "密码"}`
   - 返回: `{success: true, data: {token: "JWT令牌"}}`

2. **卡密生成**
   - 路径: `/api/admin/keys/generate`
   - 方法: POST
   - 参数: `{count: 生成数量, expireDays: 有效天数}`
   - 返回: `{success: true, data: {keys: [...生成的卡密列表]}}`

3. **卡密管理**
   - 路径: `/api/admin/keys`
   - 方法: GET
   - 参数: 可选(分页、筛选条件)
   - 返回: `{success: true, data: {total: 总数, keys: [...卡密列表]}}`

4. **文件上传**
   - 路径: `/api/admin/files/upload`
   - 方法: POST (multipart/form-data)
   - 参数: 文件数据和元信息
   - 返回: `{success: true, message: "上传成功", data: {file: {文件信息}}}`

## 安全性考虑

1. **卡密安全**
   - 卡密生成采用安全的随机算法
   - 卡密校验采用防暴力破解机制(限制尝试次数)
   - 卡密使用JWT进行会话管理

2. **文件安全**
   - 文件路径采用不可预测的命名方式
   - 文件访问需要有效的授权
   - 文件下载链接设置有效期

3. **系统安全**
   - API接口防CSRF攻击
   - 输入数据严格验证和过滤
   - 日志记录关键操作

## 部署方案

### 开发环境
- Node.js v16+
- MySQL 8.0
- 前端开发服务器: localhost:3000
- 后端API服务器: localhost:5000

### 生产环境
- 服务器: Windows Server或Linux服务器
- Web服务器: Nginx
- 进程管理: PM2
- 数据库: MySQL 8.0
- HTTPS配置

## 项目进度规划

### 第一阶段 (1-2周)
- 系统需求分析和设计
- 数据库设计与实现
- 基础架构搭建

### 第二阶段 (2-3周)
- 后端API开发
- 卡密系统实现
- 文件管理基础功能

### 第三阶段 (2-3周)
- 前端用户界面开发
- 前端管理界面开发
- 前后端联调

### 第四阶段 (1周)
- 系统测试与Bug修复
- 性能优化
- 部署上线

## 项目维护计划

1. **定期更新**
   - 每月安全更新
   - 每季度功能迭代

2. **数据备份**
   - 每日数据库自动备份
   - 每周文件系统备份

3. **监控方案**
   - 系统运行状态监控
   - 异常访问监控
   - 性能监控

## 扩展性考虑

1. **功能扩展**
   - 支持更多文件格式的在线预览
   - 增加用户反馈机制
   - 集成第三方支付系统(卡密购买)

2. **技术扩展**
   - 支持分布式部署
   - 文件存储迁移至云存储
   - API接口版本控制 