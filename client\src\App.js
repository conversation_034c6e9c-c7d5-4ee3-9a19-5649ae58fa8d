import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { checkAuthStatus, logout } from './store/slices/authSlice';
import { checkAdminStatus } from './store/slices/adminSlice';
import { Spin, notification } from 'antd';
import { WarningOutlined } from '@ant-design/icons';

// 导入页面组件
import Home from './pages/Home';
import Login from './pages/Login';
import FileList from './pages/FileList';
import FileDetails from './pages/FileDetails';
import AdminLogin from './pages/admin/AdminLogin';
import AdminDashboard from './pages/admin/Dashboard';
import AdminCardKeys from './pages/admin/CardKeys';
import AdminFiles from './pages/admin/Files';
import AdminCategories from './pages/admin/Categories';
import ExtractionRecords from './pages/admin/ExtractionRecords';
import ChangePassword from './pages/admin/ChangePassword';
import DataAnalytics from './pages/admin/DataAnalytics';
import NotFound from './pages/NotFound';

// 导入布局组件
import UserLayout from './components/layouts/UserLayout';
import AdminLayout from './components/layouts/AdminLayout';

// 保护用户路由
const ProtectedRoute = ({ children }) => {
  const dispatch = useDispatch();
  const { isAuthenticated, isReuse, loading } = useSelector(state => state.auth);
  const navigate = useNavigate();
  
  // 检查是否是重复使用的卡密
  useEffect(() => {
    if (isReuse) {
      notification.warning({
        message: '重复使用卡密提示',
        description: '该设备已使用过此卡密，无法再次提取文件。',
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
        duration: 5,
        onClose: () => {
          // 退出登录并强制跳转到首页
          dispatch(logout());
          navigate('/', { replace: true });
        }
      });
      
      // 立即退出登录
      dispatch(logout());
      // 强制跳转到首页
      navigate('/', { replace: true });
      return;
    }
  }, [isReuse, dispatch, navigate]);
  
  // 在初始挂载和更新时立即检查，避免显示受保护的内容
  if (isReuse || !isAuthenticated) {
    return <Navigate to="/" replace />;
  }
  
  if (loading) {
    return (
      <div className="flex-center" style={{ height: '100vh' }}>
        <Spin size="large" tip="正在加载..." />
      </div>
    );
  }
  
  return isAuthenticated && !isReuse ? children : <Navigate to="/" replace />;
};

// 保护管理员路由
const AdminProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useSelector(state => state.admin);
  
  if (loading) {
    return (
      <div className="flex-center" style={{ height: '100vh' }}>
        <Spin size="large" tip="正在加载..." />
      </div>
    );
  }
  
  return isAuthenticated ? children : <Navigate to="/admin/login" />;
};

function AppContent() {
  const dispatch = useDispatch();
  
  useEffect(() => {
    // 检查用户登录状态
    const userToken = localStorage.getItem('userToken');
    if (userToken) {
      dispatch(checkAuthStatus());
    }
    
    // 检查管理员登录状态
    const adminToken = localStorage.getItem('adminToken');
    if (adminToken) {
      dispatch(checkAdminStatus());
    }
  }, [dispatch]);

  return (
    <Routes>
      {/* 用户前台路由 */}
      <Route path="/" element={<Home />} />
      <Route path="/login" element={<Login />} />
      
      <Route path="/files" element={
        <ProtectedRoute>
          <UserLayout>
            <FileList />
          </UserLayout>
        </ProtectedRoute>
      } />
      
      <Route path="/files/:id" element={
        <ProtectedRoute>
          <UserLayout>
            <FileDetails />
          </UserLayout>
        </ProtectedRoute>
      } />
      
      {/* 管理员后台路由 */}
      <Route path="/admin/login" element={<AdminLogin />} />
      
      <Route path="/admin" element={
        <AdminProtectedRoute>
          <AdminLayout>
            <AdminDashboard />
          </AdminLayout>
        </AdminProtectedRoute>
      } />

      <Route path="/admin/data-analytics" element={
        <AdminProtectedRoute>
          <AdminLayout>
            <DataAnalytics />
          </AdminLayout>
        </AdminProtectedRoute>
      } />

      <Route path="/admin/card-keys" element={
        <AdminProtectedRoute>
          <AdminLayout>
            <AdminCardKeys />
          </AdminLayout>
        </AdminProtectedRoute>
      } />
      
      <Route path="/admin/files" element={
        <AdminProtectedRoute>
          <AdminLayout>
            <AdminFiles />
          </AdminLayout>
        </AdminProtectedRoute>
      } />
      
      <Route path="/admin/categories" element={
        <AdminProtectedRoute>
          <AdminLayout>
            <AdminCategories />
          </AdminLayout>
        </AdminProtectedRoute>
      } />
      
      <Route path="/admin/extraction-records" element={
        <AdminProtectedRoute>
          <AdminLayout>
            <ExtractionRecords />
          </AdminLayout>
        </AdminProtectedRoute>
      } />
      
      <Route path="/admin/change-password" element={
        <AdminProtectedRoute>
          <ChangePassword />
        </AdminProtectedRoute>
      } />
      
      {/* 404页面 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App; 