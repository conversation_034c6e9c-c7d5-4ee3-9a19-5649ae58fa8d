@echo off
echo ======================================
echo    File Integrity Verification System
echo ======================================
echo.
echo This tool will calculate hash values for authorization-related files
echo to verify file integrity when starting the system.
echo Note: Run this tool after updating the system or modifying authorization code.
echo.
echo Initializing file integrity verification system...

cd /d %~dp0
node server/utils/initializeHashes.js

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Initialization failed! Please check the error message and try again.
    echo.
    pause
    exit /b 1
)

echo.
echo ======================================
echo [SUCCESS] Initialization completed!
echo     The system will now verify file integrity at startup.
echo ======================================
echo.
echo Press any key to exit...
pause > nul 