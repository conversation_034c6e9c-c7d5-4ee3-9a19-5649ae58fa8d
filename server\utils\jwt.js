const jwt = require('jsonwebtoken');

// 默认JWT密钥，生产环境应从环境变量获取
const SECRET_KEY = process.env.JWT_SECRET || 'zizhutihuo_jwt_secret_key_2024';

/**
 * 生成JWT令牌
 * @param {Object} payload - 要编码到令牌中的数据
 * @param {String} expiresIn - 令牌有效期（默认2小时）
 * @returns {String} JWT令牌
 */
const generateToken = (payload, expiresIn = '2h') => {
  return jwt.sign(payload, SECRET_KEY, { expiresIn });
};

/**
 * 验证JWT令牌
 * @param {String} token - 要验证的JWT令牌
 * @returns {Object|null} 解码后的数据或null（验证失败）
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, SECRET_KEY);
  } catch (error) {
    return null;
  }
};

module.exports = {
  generateToken,
  verifyToken
}; 