const { CardKey, File } = require('../models');
const { Op } = require('sequelize');
const { batchGenerateKeys, generateKeysWithPrefix } = require('../utils/keyGenerator');
const { sequelize } = require('../config/database');
const { cleanupExpiredCardKeys, cleanupUsedCardKeys, cleanupInvalidCardKeys } = require('../utils/cleanupTasks');

/**
 * 生成卡密
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const generateKeys = async (req, res) => {
  try {
    const { count = 1, expireDays, prefix = '', keyType = 'single', maxDevices = 1 } = req.body;
    
    // 验证卡密类型参数
    if (keyType !== 'single' && keyType !== 'multi') {
      return res.status(400).json({ success: false, message: '无效的卡密类型参数' });
    }
    
    // 验证最大设备数参数
    if (keyType === 'multi' && (!Number.isInteger(maxDevices) || maxDevices < 1)) {
      return res.status(400).json({ success: false, message: '无效的最大设备数参数' });
    }
    
    // 生成卡密
    const keyStrings = prefix ? 
      generateKeysWithPrefix(prefix, count) : 
      batchGenerateKeys(count);
    
    // 计算过期时间
    let expireTime = null;
    if (expireDays) {
      expireTime = new Date();
      expireTime.setDate(expireTime.getDate() + parseInt(expireDays));
    }
    
    // 批量创建卡密记录
    const keysData = keyStrings.map(key_code => ({
      key_code,
      expire_time: expireTime,
      status: true,
      key_type: keyType,
      max_devices: keyType === 'multi' ? maxDevices : 1
    }));
    
    const createdKeys = await CardKey.bulkCreate(keysData);
    
    return res.json({
      success: true,
      message: `成功生成 ${createdKeys.length} 个${keyType === 'multi' ? '多设备' : '单设备'}卡密`,
      data: { 
        keys: createdKeys.map(key => ({
          id: key.id,
          key_code: key.key_code,
          expire_time: key.expire_time,
          key_type: key.key_type,
          max_devices: key.max_devices
        }))
      }
    });
  } catch (error) {
    console.error('生成卡密错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取卡密列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getCardKeys = async (req, res) => {
  try {
    const { page = 1, limit = 10, is_used, status } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const where = {};
    if (is_used !== undefined) {
      where.is_used = is_used === 'true';
    }
    if (status !== undefined) {
      // 只处理状态值为0和1的情况，因为模型中status是布尔值
      if (status === '0') {
        where.status = false;
      } else if (status === '1') {
        where.status = true;
      }
      // 状态值为2时表示已过期，需要特殊处理
      else if (status === '2') {
        where.expire_time = {
          [Op.not]: null,
          [Op.lt]: new Date()
        };
        where.status = true; // 基础状态仍为有效
      }
    }
    
    // 获取卡密列表
    const { count, rows } = await CardKey.findAndCountAll({
      where,
      include: [
        { 
          model: File, 
          as: 'usedFile',
          attributes: ['id', 'file_name'],
          required: false
        }
      ],
      order: [['create_time', 'DESC']],
      offset,
      limit: parseInt(limit)
    });
    
    return res.json({
      success: true,
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        keys: rows
      }
    });
  } catch (error) {
    console.error('获取卡密列表错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 更新卡密状态
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateCardKeyStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (status === undefined) {
      return res.status(400).json({ success: false, message: '状态参数不能为空' });
    }
    
    // 查询卡密
    const cardKey = await CardKey.findByPk(id);
    
    if (!cardKey) {
      return res.status(404).json({ success: false, message: '卡密不存在' });
    }
    
    // 更新状态
    await cardKey.update({ status: Boolean(status) });
    
    return res.json({
      success: true,
      message: '卡密状态更新成功',
      data: { cardKey }
    });
  } catch (error) {
    console.error('更新卡密状态错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 批量更新卡密过期时间
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchUpdateExpireTime = async (req, res) => {
  try {
    const { ids, expireDays } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, message: '请选择要更新的卡密' });
    }
    
    if (!expireDays) {
      return res.status(400).json({ success: false, message: '过期天数不能为空' });
    }
    
    // 计算新的过期时间
    const expireTime = new Date();
    expireTime.setDate(expireTime.getDate() + parseInt(expireDays));
    
    // 批量更新
    const [updated] = await CardKey.update(
      { expire_time: expireTime },
      { where: { id: { [Op.in]: ids } } }
    );
    
    return res.json({
      success: true,
      message: `成功更新 ${updated} 个卡密的过期时间`,
      data: { updated }
    });
  } catch (error) {
    console.error('批量更新卡密过期时间错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 删除卡密
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteCardKey = async (req, res) => {
  // 创建事务
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    
    // 查询卡密是否存在
    const cardKey = await CardKey.findByPk(id, { transaction });
    
    if (!cardKey) {
      await transaction.rollback();
      return res.status(404).json({ success: false, message: '卡密不存在' });
    }
    
    // 先清除外键关联，解除与文件的关联，无论是否被使用
    await sequelize.query(`UPDATE card_keys SET used_file_id = NULL WHERE id = ?`, {
      replacements: [id],
      transaction
    });
    
    // 再删除卡密记录
    await sequelize.query(`DELETE FROM card_keys WHERE id = ?`, {
      replacements: [id],
      transaction
    });
    
    // 提交事务
    await transaction.commit();
    
    return res.json({
      success: true,
      message: '卡密删除成功',
      data: { id }
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('删除卡密错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误，删除卡密失败' });
  }
};

/**
 * 批量删除卡密
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchDeleteCardKeys = async (req, res) => {
  // 创建事务
  const transaction = await sequelize.transaction();
  
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ success: false, message: '请选择要删除的卡密' });
    }
    
    // 先清除外键关联，批量更新卡密的used_file_id为NULL
    await sequelize.query(`UPDATE card_keys SET used_file_id = NULL WHERE id IN (?)`, {
      replacements: [ids],
      transaction
    });
    
    // 再批量删除卡密
    const [, result] = await sequelize.query(`DELETE FROM card_keys WHERE id IN (?)`, {
      replacements: [ids],
      transaction
    });
    
    // 提交事务
    await transaction.commit();
    
    return res.json({
      success: true,
      message: `成功删除 ${ids.length} 个卡密`,
      data: { deleted: ids.length, ids }
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('批量删除卡密错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误，批量删除卡密失败' });
  }
};

/**
 * 手动清理30天前过期和使用过的卡密
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const manualCleanupExpiredCardKeys = async (req, res) => {
  try {
    const result = await cleanupExpiredCardKeys();
    
    if (result.success) {
      return res.json({
        success: true,
        message: result.message,
        data: { deletedCount: result.deletedCount }
      });
    } else {
      return res.status(500).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    console.error('手动清理过期卡密错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误，清理卡密失败' });
  }
};

/**
 * 手动清理所有已使用的卡密
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const manualCleanupUsedCardKeys = async (req, res) => {
  try {
    const result = await cleanupUsedCardKeys();
    
    if (result.success) {
      return res.json({
        success: true,
        message: result.message,
        data: { deletedCount: result.deletedCount }
      });
    } else {
      return res.status(500).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    console.error('手动清理已使用卡密错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误，清理已使用卡密失败' });
  }
};

/**
 * 手动清理所有无效卡密（已使用、已过期、状态为无效的）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const manualCleanupInvalidCardKeys = async (req, res) => {
  try {
    const result = await cleanupInvalidCardKeys();
    
    if (result.success) {
      return res.json({
        success: true,
        message: result.message,
        data: { deletedCount: result.deletedCount }
      });
    } else {
      return res.status(500).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    console.error('手动清理无效卡密错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误，清理无效卡密失败' });
  }
};

module.exports = {
  generateKeys,
  getCardKeys,
  updateCardKeyStatus,
  batchUpdateExpireTime,
  deleteCardKey,
  batchDeleteCardKeys,
  manualCleanupExpiredCardKeys,
  manualCleanupUsedCardKeys,
  manualCleanupInvalidCardKeys
}; 