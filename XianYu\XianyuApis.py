import json
import time

import requests

from utils.xianyu_utils import generate_sign, trans_cookies, generate_device_id


class XianyuApis:
    def __init__(self):
        self.url = 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/'

    def get_token(self, cookies, device_id):
        headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.goofish.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        }
        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idlemessage.pc.login.token',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.im.0.0',
        }
        data_val = '{"appKey":"444e9908a51d1cb236a27862abc769c9","deviceId":"' + device_id + '"}'
        data = {
            'data': data_val,
        }
        token = cookies['_m_h5_tk'].split('_')[0]
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign
        response = requests.post('https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/', params=params, cookies=cookies, headers=headers, data=data)
        res_json = response.json()
        return res_json

    def get_seller_items(self, cookies, device_id, seller_id=None, page_num=1, page_size=20):
        """
        获取卖家商品列表

        Args:
            cookies: 用户cookie字典
            device_id: 设备ID
            seller_id: 卖家ID，如果为None则获取当前用户的商品
            page_num: 页码，从1开始
            page_size: 每页数量，默认20

        Returns:
            包含商品列表的响应数据
        """
        headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.goofish.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        }

        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.idle.web.xyh.item.list',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.personal.0.0',
        }

        # 构造请求数据
        data_payload = {
            "pageNumber": str(page_num),
            "pageSize": str(page_size),
            "deviceId": device_id
        }

        # 如果指定了卖家ID，添加到请求中；否则使用当前用户ID
        if seller_id:
            data_payload["userId"] = seller_id
        else:
            # 从cookies中获取当前用户ID
            current_user_id = cookies.get('unb', '')
            if current_user_id:
                data_payload["userId"] = current_user_id

        data_val = json.dumps(data_payload, separators=(',', ':'))
        data = {
            'data': data_val,
        }

        # 生成签名
        token = cookies['_m_h5_tk'].split('_')[0]
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign

        # 发送请求
        response = requests.post('https://h5api.m.goofish.com/h5/mtop.idle.web.xyh.item.list/1.0/',
                               params=params, cookies=cookies, headers=headers, data=data)

        try:
            res_json = response.json()
            return res_json
        except json.JSONDecodeError:
            return {'ret': ['FAIL_SYS_ILLEGAL_ACCESS::请求失败'], 'data': None}

    def get_item_detail(self, cookies, device_id, item_id):
        """
        获取商品详情

        Args:
            cookies: 用户cookie字典
            device_id: 设备ID
            item_id: 商品ID

        Returns:
            包含商品详情的响应数据
        """
        headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.goofish.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        }

        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idle.pc.detail',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.item.0.0',
        }

        # 构造请求数据
        data_payload = {
            "itemId": str(item_id),
            "deviceId": device_id
        }

        data_val = json.dumps(data_payload, separators=(',', ':'))
        data = {
            'data': data_val,
        }

        # 生成签名
        token = cookies['_m_h5_tk'].split('_')[0]
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign

        # 发送请求
        response = requests.post('https://h5api.m.goofish.com/h5/mtop.taobao.idle.pc.detail/1.0/',
                               params=params, cookies=cookies, headers=headers, data=data)

        try:
            res_json = response.json()
            return res_json
        except json.JSONDecodeError:
            return {'ret': ['FAIL_SYS_ILLEGAL_ACCESS::请求失败'], 'data': None}

    def _get_status_text(self, status_code):
        """将状态码转换为状态文本"""
        status_map = {
            0: '在售',
            1: '在售',
            2: '已售出',
            3: '已下架',
            4: '审核中',
            5: '违规下架'
        }
        return status_map.get(status_code, f'未知状态({status_code})')

    def parse_item_detail(self, response_data):
        """
        解析商品详情响应数据

        Args:
            response_data: API响应数据

        Returns:
            解析后的商品详情数据
        """
        result = {
            'success': False,
            'message': '',
            'data': {}
        }

        if not response_data:
            result['message'] = '响应数据为空'
            return result

        # 检查请求是否成功
        if 'ret' in response_data and response_data['ret'][0] != 'SUCCESS::调用成功':
            result['message'] = response_data['ret'][0] if response_data['ret'] else '请求失败'
            return result

        if 'data' not in response_data:
            result['message'] = '响应中缺少data字段'
            return result

        data = response_data['data']

        # 如果data是字符串，尝试解析JSON
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                result['message'] = '数据解析失败'
                return result

        # 解析商品详情
        if isinstance(data, dict):
            # 提取基本信息
            item_do = data.get('itemDO', {})
            share_data = item_do.get('shareData', {})

            # 解析分享信息中的详细描述
            desc = ''
            share_info_str = share_data.get('shareInfoJsonString', '')
            if share_info_str:
                try:
                    share_info = json.loads(share_info_str)
                    main_params = share_info.get('contentParams', {}).get('mainParams', {})
                    desc = main_params.get('content', '')
                except json.JSONDecodeError:
                    pass

            # 提取图片信息
            images = []
            if share_info_str:
                try:
                    share_info = json.loads(share_info_str)
                    main_params = share_info.get('contentParams', {}).get('mainParams', {})
                    image_list = main_params.get('images', [])
                    images = [img.get('image', '') for img in image_list if img.get('image')]
                except json.JSONDecodeError:
                    pass

            # 提取价格和标题信息
            title = ''
            price = ''
            if share_info_str:
                try:
                    share_info = json.loads(share_info_str)
                    header_params = share_info.get('contentParams', {}).get('headerParams', {})
                    title = header_params.get('title', '')

                    main_params = share_info.get('contentParams', {}).get('mainParams', {})
                    extra = main_params.get('extra', {})
                    price = extra.get('soldPrice', '')
                except json.JSONDecodeError:
                    pass

            detail_info = {
                'itemId': data.get('itemId', ''),
                'title': title or data.get('title', ''),
                'desc': desc,  # 从shareInfoJsonString中提取的完整描述
                'price': price or data.get('price', ''),
                'originalPrice': data.get('originalPrice', ''),
                'images': images or data.get('images', []),
                'mainImage': images[0] if images else data.get('mainImage', ''),
                'status': data.get('status', ''),
                'statusText': self._get_status_text(data.get('status', 0)),
                'viewCount': data.get('viewCount', 0),
                'likeCount': data.get('likeCount', 0),
                'commentCount': data.get('commentCount', 0),
                'publishTime': data.get('publishTime', ''),
                'updateTime': data.get('updateTime', ''),
                'location': data.get('location', ''),
                'category': data.get('category', ''),
                'categoryName': data.get('categoryName', ''),
                'sellerId': data.get('sellerId', ''),
                'sellerNick': data.get('sellerNick', ''),
                'sellerAvatar': data.get('sellerAvatar', ''),
                'tags': data.get('tags', []),
                'attributes': data.get('attributes', {}),
                'shipping': data.get('shipping', {}),
                'contact': data.get('contact', {}),
                'shareData': share_data  # 保留原始分享数据
            }

            result['success'] = True
            result['message'] = '获取成功'
            result['data'] = detail_info
        else:
            result['message'] = '数据格式错误'

        return result

    def parse_seller_items(self, response_data):
        """
        解析卖家商品列表响应数据

        Args:
            response_data: API响应数据

        Returns:
            解析后的商品列表数据
        """
        result = {
            'success': False,
            'message': '',
            'data': {
                'items': [],
                'total': 0,
                'hasMore': False,
                'currentPage': 1
            }
        }

        if not response_data:
            result['message'] = '响应数据为空'
            return result

        # 检查请求是否成功
        if 'ret' in response_data and response_data['ret'][0] != 'SUCCESS::调用成功':
            result['message'] = response_data['ret'][0] if response_data['ret'] else '请求失败'
            return result

        if 'data' not in response_data:
            result['message'] = '响应中缺少data字段'
            return result

        data = response_data['data']

        # 如果data是字符串，尝试解析JSON
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                result['message'] = '数据解析失败'
                return result

        # 解析商品列表
        items = []
        item_list = []

        # 尝试不同的数据结构路径
        if isinstance(data, dict):
            if 'cardList' in data:
                item_list = data['cardList']
            elif 'itemList' in data:
                item_list = data['itemList']
            elif 'items' in data:
                item_list = data['items']
            elif 'result' in data and isinstance(data['result'], dict):
                if 'itemList' in data['result']:
                    item_list = data['result']['itemList']
                elif 'items' in data['result']:
                    item_list = data['result']['items']
        elif isinstance(data, list):
            item_list = data

        # 解析每个商品
        for item_data in item_list:
            if not isinstance(item_data, dict):
                continue

            # 检查是否有cardData结构
            if 'cardData' in item_data:
                card_data = item_data['cardData']

                # 提取基本信息
                item_id = card_data.get('id', '')
                title = card_data.get('title', '')

                # 提取价格信息
                price_info = card_data.get('priceInfo', {})
                price = price_info.get('price', '')
                if price_info.get('preText'):
                    price = price_info.get('preText', '') + price

                # 提取图片信息
                pic_info = card_data.get('picInfo', {})
                main_image = pic_info.get('picUrl', '')

                # 提取详细参数
                detail_params = card_data.get('detailParams', {})

                item = {
                    'itemId': item_id,
                    'title': title,
                    'price': price,
                    'originalPrice': detail_params.get('soldPrice', ''),
                    'images': [main_image] if main_image else [],
                    'mainImage': main_image,
                    'status': str(card_data.get('itemStatus', '')),
                    'statusText': self._get_status_text(card_data.get('itemStatus', 0)),
                    'viewCount': 0,  # 这个API没有提供浏览量
                    'likeCount': 0,  # 这个API没有提供点赞数
                    'commentCount': 0,  # 这个API没有提供评论数
                    'publishTime': '',  # 这个API没有提供发布时间
                    'updateTime': '',
                    'location': '',  # 这个API没有提供地区信息
                    'category': str(card_data.get('categoryId', '')),
                    'description': detail_params.get('title', title),
                    'sellerId': '',  # 这个API没有直接提供卖家ID
                    'sellerNick': ''  # 这个API没有直接提供卖家昵称
                }
            else:
                # 原有的解析逻辑（兼容其他数据结构）
                item = {
                    'itemId': item_data.get('itemId', item_data.get('id', '')),
                    'title': item_data.get('title', item_data.get('name', '')),
                    'price': item_data.get('price', item_data.get('realPrice', '')),
                    'originalPrice': item_data.get('originalPrice', ''),
                    'images': item_data.get('images', item_data.get('picUrl', [])),
                    'mainImage': '',
                    'status': item_data.get('status', item_data.get('itemStatus', '')),
                    'statusText': item_data.get('statusText', ''),
                    'viewCount': item_data.get('viewCount', 0),
                    'likeCount': item_data.get('likeCount', 0),
                    'commentCount': item_data.get('commentCount', 0),
                    'publishTime': item_data.get('publishTime', item_data.get('createTime', '')),
                    'updateTime': item_data.get('updateTime', ''),
                    'location': item_data.get('location', item_data.get('area', '')),
                    'category': item_data.get('category', ''),
                    'description': item_data.get('description', item_data.get('desc', '')),
                    'sellerId': item_data.get('sellerId', item_data.get('userId', '')),
                    'sellerNick': item_data.get('sellerNick', item_data.get('nick', ''))
                }

            # 处理图片列表，提取主图
            if isinstance(item['images'], list) and item['images']:
                item['mainImage'] = item['images'][0]
            elif isinstance(item['images'], str):
                item['mainImage'] = item['images']
                item['images'] = [item['images']]

            items.append(item)

        # 设置返回结果
        result['success'] = True
        result['message'] = '获取成功'
        result['data']['items'] = items
        result['data']['total'] = len(items)

        # 尝试获取分页信息
        if isinstance(data, dict):
            result['data']['total'] = data.get('totalCount', len(items))
            result['data']['hasMore'] = data.get('nextPage', False)
            result['data']['currentPage'] = 1  # API没有返回当前页信息

        return result


if __name__ == '__main__':
    # 使用示例
    cookies_str = r''  # 请在这里填入您的完整cookie字符串

    if not cookies_str:
        print("请先设置有效的cookies_str")
        print("Cookie获取方法：")
        print("1. 登录闲鱼网页版 https://www.goofish.com")
        print("2. 打开浏览器开发者工具(F12)")
        print("3. 在Network标签页中找到任意请求")
        print("4. 复制Request Headers中的Cookie值")
        exit()

    # 初始化API
    xianyu = XianyuApis()

    try:
        # 解析cookies
        cookies = trans_cookies(cookies_str)
        print("Cookie解析成功")

        # 获取用户ID
        user_id = cookies.get('unb', '')
        if not user_id:
            print("错误：Cookie中未找到用户ID(unb字段)")
            print("请确保Cookie是完整的，包含unb字段")
            exit()

        print(f"当前用户ID: {user_id}")

        # 生成设备ID
        device_id = generate_device_id(user_id)
        print(f"设备ID: {device_id}")

        print("\n=== 获取商品列表 ===")

        # 获取第一页商品
        response = xianyu.get_seller_items(
            cookies=cookies,
            device_id=device_id,
            page_num=1,
            page_size=10
        )

        print("原始响应数据:")
        print(json.dumps(response, ensure_ascii=False, indent=2))

        # 解析商品数据
        parsed_result = xianyu.parse_seller_items(response)

        print(f"\n解析结果:")
        print(f"成功: {parsed_result['success']}")
        print(f"消息: {parsed_result['message']}")

        if parsed_result['success']:
            items = parsed_result['data']['items']
            total = parsed_result['data']['total']

            print(f"商品总数: {total}")
            print(f"当前页商品数: {len(items)}")

            if items:
                print("\n商品列表:")
                for i, item in enumerate(items, 1):
                    print(f"\n{i}. 商品信息:")
                    print(f"   ID: {item['itemId']}")
                    print(f"   标题: {item['title']}")
                    print(f"   价格: {item['price']}")
                    print(f"   状态: {item['statusText'] or item['status']}")
                    print(f"   浏览量: {item['viewCount']}")
                    print(f"   点赞数: {item['likeCount']}")
                    print(f"   发布时间: {item['publishTime']}")
                    if item['mainImage']:
                        print(f"   主图: {item['mainImage']}")
            else:
                print("当前没有商品")
        else:
            print(f"获取失败: {parsed_result['message']}")

    except KeyError as e:
        print(f"Cookie格式错误，缺少必要字段: {e}")
        print("请确保Cookie包含_m_h5_tk和unb字段")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
