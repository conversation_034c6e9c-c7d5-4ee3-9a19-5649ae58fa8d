# 项目数据库初始化步骤

## 前置要求

1. 确保已安装 Node.js v16+ 版本
2. 确保已安装 MySQL 8.0+ 版本，且能正常运行
3. MySQL用户名为`root`，密码为`123456`（如需修改连接信息，请查看后续步骤）

## 数据库初始化步骤

### 一：手动初始化数据库

如果您想手动控制数据库初始化过程，请按照以下步骤操作：

1. 如果需要修改默认的数据库连接信息，请在项目server目录的`.env`，init-db.js，/config/database.js文件并设置以下环境变量：
   ```
   DB_HOST=localhost
   DB_USER=root
   DB_PASS=123456（自己电脑的mysql密码）
   DB_NAME=zizhutihuoxitong
   ```

3. 运行数据库初始化脚本：
   ```
   cd server
   node init-db.js
   ```
   
4. 验证数据库是否正确初始化：
   ```
   node test-db.js
   ```

## 重要说明

1. 数据库初始化会创建一个名为`zizhutihuoxitong`的数据库，字符集为`utf8mb4`，排序规则为`utf8mb4_unicode_ci`
2. 系统会自动导入管理员账户，默认管理员账号为：`admin`，密码为：12345678
3. 系统首次启动时需要授权验证，授权码一经使用将与当前设备绑定
4. 后端API默认运行在`http://localhost:5000/api`
5. 前端界面默认运行在`http://localhost:3000`

## 可能遇到的问题及解决方法

- **MySQL连接失败**：
  - 检查MySQL服务是否正在运行
  - 验证用户名和密码是否正确
  - 如需使用不同的连接信息，请修改server/`.env`或`server/config/database.js`
- **依赖安装错误**：
  - 确保网络连接正常
  - 尝试使用`npm install --force`
  - 确保Node.js版本符合要求（v16+） 