const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const OperationLog = sequelize.define('OperationLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  operation_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '操作类型'
  },
  operation_desc: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '操作描述'
  },
  operator_ip: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '操作IP'
  },
  card_key: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '相关卡密'
  },
  file_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '相关文件ID'
  },
  create_time: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '操作时间'
  }
}, {
  tableName: 'operation_logs',
  timestamps: false
});

module.exports = OperationLog; 