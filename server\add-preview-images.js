const { sequelize } = require('./config/database');

async function addPreviewImagesField() {
  try {
    console.log('开始添加简介图片字段...');

    // 执行原始SQL来修改files表，添加preview_images字段
    await sequelize.query(`
      ALTER TABLE files 
      ADD COLUMN preview_images TEXT NULL COMMENT '简介图片路径JSON数组' AFTER important_text
    `);

    console.log('简介图片字段添加成功！');
    process.exit(0);
  } catch (error) {
    console.error('添加简介图片字段失败:', error);
    process.exit(1);
  }
}

// 执行更新
addPreviewImagesField();
