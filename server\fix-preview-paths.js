const { sequelize } = require('./config/database');
const File = require('./models/File');

async function fixPreviewPaths() {
  try {
    console.log('开始修复简介图片路径...');

    // 更新文件35的简介图片路径
    await File.update({
      preview_images: JSON.stringify(['preview/1754153990441-b7c98292-c9f9-409b-8cd6-d598712d51d6.jpeg'])
    }, {
      where: { id: 35 }
    });

    console.log('简介图片路径修复成功！');
    process.exit(0);
  } catch (error) {
    console.error('修复简介图片路径失败:', error);
    process.exit(1);
  }
}

// 执行修复
fixPreviewPaths();
