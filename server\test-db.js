const { sequelize } = require('./config/database');
const { CardKey } = require('./models');

async function testDb() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询CardKey表结构
    const cardKeyAttributes = CardKey.rawAttributes;
    console.log('CardKey模型字段:');
    for (const fieldName in cardKeyAttributes) {
      const field = cardKeyAttributes[fieldName];
      console.log(`- ${fieldName}: ${field.type.key || field.type}`);
    }
    
    // 查询卡密数据
    const cardKeys = await CardKey.findAll({ limit: 5 });
    console.log('\nCardKey数据示例:');
    cardKeys.forEach(key => {
      console.log(key.toJSON());
    });

    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testDb(); 