const fs = require('fs');
const path = require('path');
const { initializeFileHashes } = require('./fileIntegrityValidator');

// 获取项目根目录
const basePath = path.resolve(__dirname, '../..');

// 初始化文件哈希值
const fileHashes = initializeFileHashes(basePath);

// 写入哈希值到JSON文件
const hashesPath = path.join(__dirname, 'hashValues.json');
fs.writeFileSync(hashesPath, JSON.stringify(fileHashes, null, 2));

console.log(`哈希值已保存到: ${hashesPath}`);
console.log('文件完整性验证系统已初始化完成'); 