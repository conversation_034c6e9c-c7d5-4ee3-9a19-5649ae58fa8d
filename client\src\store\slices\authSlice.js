import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// 验证卡密
export const verifyCard = createAsyncThunk(
  'auth/verifyCard',
  async (cardKey, { rejectWithValue }) => {
    try {
      const response = await axios.post('/api/auth/verify', { key_code: cardKey });
      const { token } = response.data.data;
      const isReuse = response.data.isReuse || false;
      
      // 保存令牌到本地存储
      localStorage.setItem('userToken', token);
      
      // 不再设置全局请求头
      // axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      return { token, isReuse };
    } catch (error) {
      const errorMsg = error.response?.data?.message || '卡密验证失败，请检查卡密是否正确';
      console.error('验证卡密失败:', errorMsg);
      return rejectWithValue(errorMsg);
    }
  }
);

// 检查登录状态
export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const token = localStorage.getItem('userToken');
      
      if (!token) {
        return rejectWithValue('未登录');
      }
      
      // 调用专门的验证接口，检查令牌有效性并获取重用状态
      const response = await axios.get('/api/auth/check', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // 检查令牌是否有效
      if (!response.data.success) {
        localStorage.removeItem('userToken');
        return rejectWithValue(response.data.message || '会话已过期');
      }
      
      // 如果是重复使用卡密，返回特殊状态并清理登录状态
      const isReuse = response.data.isReuse || false;
      if (isReuse) {
        console.log('检测到重复使用卡密，即将登出');
        setTimeout(() => {
          dispatch(logout());
        }, 1000);
        return { isAuthenticated: false, token: null, isReuse: true };
      }
      
      console.log('卡密验证成功，状态正常');
      return { isAuthenticated: true, token, isReuse: false };
    } catch (error) {
      console.error('验证令牌发生错误:', error);
      localStorage.removeItem('userToken');
      return rejectWithValue(error.response?.data?.message || '会话已过期');
    }
  }
);

// 退出登录
export const logout = createAsyncThunk(
  'auth/logout',
  async () => {
    localStorage.removeItem('userToken');
    // 不再修改全局请求头
    // delete axios.defaults.headers.common['Authorization'];
    return { isAuthenticated: false };
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    isAuthenticated: false,
    token: null,
    loading: false,
    error: null,
    isReuse: false
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearReuse: (state) => {
      state.isReuse = false;
    }
  },
  extraReducers: (builder) => {
    builder
      // 验证卡密
      .addCase(verifyCard.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.isReuse = false;
      })
      .addCase(verifyCard.fulfilled, (state, action) => {
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.isReuse = action.payload.isReuse;
        state.loading = false;
      })
      .addCase(verifyCard.rejected, (state, action) => {
        state.isAuthenticated = false;
        state.token = null;
        state.loading = false;
        state.error = action.payload;
      })
      
      // 检查登录状态
      .addCase(checkAuthStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isAuthenticated = action.payload.isAuthenticated;
        state.token = action.payload.token;
        state.isReuse = action.payload.isReuse;
        state.loading = false;
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.isAuthenticated = false;
        state.token = null;
        state.isReuse = false;
        state.loading = false;
        state.error = action.payload;
      })
      
      // 退出登录
      .addCase(logout.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.token = null;
        state.isReuse = false;
      });
  }
});

export const { clearError, clearReuse } = authSlice.actions;
export default authSlice.reducer; 