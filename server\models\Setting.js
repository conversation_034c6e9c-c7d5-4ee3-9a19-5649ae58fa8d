const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Setting = sequelize.define('Setting', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '设置ID'
  },
  setting_key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '设置键名'
  },
  setting_value: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '设置值'
  },
  setting_type: {
    type: DataTypes.STRING(50),
    defaultValue: 'string',
    comment: '设置类型：string, number, boolean, json'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '设置描述'
  },
  is_encrypted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否加密存储'
  }
}, {
  tableName: 'settings',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  comment: '系统设置表'
});

// 静态方法：获取设置值
Setting.getValue = async function(key, defaultValue = null) {
  try {
    const setting = await this.findOne({
      where: { setting_key: key }
    });
    
    if (!setting) {
      return defaultValue;
    }
    
    const value = setting.setting_value;
    
    // 根据类型转换值
    switch (setting.setting_type) {
      case 'number':
        return value ? Number(value) : defaultValue;
      case 'boolean':
        return value === 'true';
      case 'json':
        try {
          return JSON.parse(value);
        } catch {
          return defaultValue;
        }
      default:
        return value || defaultValue;
    }
  } catch (error) {
    console.error('获取设置失败:', error);
    return defaultValue;
  }
};

// 静态方法：设置值
Setting.setValue = async function(key, value, type = 'string', description = null) {
  try {
    let stringValue = value;
    
    // 根据类型转换为字符串
    if (type === 'json') {
      stringValue = JSON.stringify(value);
    } else if (type === 'boolean') {
      stringValue = value ? 'true' : 'false';
    } else {
      stringValue = String(value);
    }
    
    const [setting, created] = await this.findOrCreate({
      where: { setting_key: key },
      defaults: {
        setting_key: key,
        setting_value: stringValue,
        setting_type: type,
        description: description
      }
    });
    
    if (!created) {
      await setting.update({
        setting_value: stringValue,
        setting_type: type,
        description: description || setting.description
      });
    }
    
    return setting;
  } catch (error) {
    console.error('设置值失败:', error);
    throw error;
  }
};

// 静态方法：获取闲鱼Cookie
Setting.getXianyuCookies = async function() {
  return await this.getValue('xianyu_cookies', '');
};

// 静态方法：设置闲鱼Cookie
Setting.setXianyuCookies = async function(cookies) {
  return await this.setValue('xianyu_cookies', cookies, 'string', '闲鱼Cookie字符串');
};

// 静态方法：获取闲鱼最大同步数量
Setting.getXianyuMaxItems = async function() {
  return await this.getValue('xianyu_max_items', 50);
};

// 静态方法：设置闲鱼最大同步数量
Setting.setXianyuMaxItems = async function(maxItems) {
  return await this.setValue('xianyu_max_items', maxItems, 'number', '闲鱼同步最大商品数量');
};

module.exports = Setting;
