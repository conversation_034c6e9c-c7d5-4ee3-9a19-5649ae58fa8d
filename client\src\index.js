import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import App from './App';
import { store } from './store';
import axios from 'axios';
import './index.css';

// 设置 Day.js 本地化配置
dayjs.locale('zh-cn');

// 配置 Axios 默认值
axios.defaults.baseURL = process.env.REACT_APP_API_URL || '';
axios.defaults.headers.common['Content-Type'] = 'application/json';

// 从本地存储中获取 token
const userToken = localStorage.getItem('userToken');
const adminToken = localStorage.getItem('adminToken');

// 如果有 token 则设置请求头
if (userToken) {
  axios.defaults.headers.common['Authorization'] = `Bearer ${userToken}`;
}

// 配置主题 - 浅蓝色科技风格
const theme = {
  token: {
    colorPrimary: '#40a9ff',
    colorPrimaryHover: '#69c0ff',
    colorPrimaryActive: '#1890ff',
    borderRadius: 8,
    colorBgContainer: 'rgba(255, 255, 255, 0.85)',
    colorBgLayout: '#f0f8ff',
    colorBgElevated: 'rgba(255, 255, 255, 0.9)',
    boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
    boxShadowSecondary: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
  },
  components: {
    Layout: {
      siderBg: 'rgba(255, 255, 255, 0.95)',
      bodyBg: '#f0f8ff',
    },
    Card: {
      colorBgContainer: 'rgba(255, 255, 255, 0.8)',
      boxShadowTertiary: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: 'rgba(64, 169, 255, 0.1)',
      itemHoverBg: 'rgba(64, 169, 255, 0.05)',
    },
    Button: {
      primaryShadow: '0 2px 0 rgba(64, 169, 255, 0.1)',
    },
    Table: {
      headerBg: 'rgba(240, 248, 255, 0.8)',
      rowHoverBg: 'rgba(64, 169, 255, 0.03)',
    }
  },
};

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <ConfigProvider locale={zhCN} theme={theme}>
        <App />
      </ConfigProvider>
    </Provider>
  </React.StrictMode>
); 