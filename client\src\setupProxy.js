const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:5000',
      changeOrigin: true,
      secure: false,
      pathRewrite: {
        '^/api': '/api' // 不做路径重写
      }
    })
  );

  // 修复WebSocket连接问题
  app.use(
    '/ws',
    createProxyMiddleware({
      target: 'http://localhost:5000',
      ws: true
    })
  );

  app.use(
    '/sockjs-node',
    createProxyMiddleware({
      target: 'http://localhost:3000',
      ws: true
    })
  );
}; 