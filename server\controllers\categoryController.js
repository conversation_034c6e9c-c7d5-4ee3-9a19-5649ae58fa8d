const { Category, File } = require('../models');
const { Op } = require('sequelize');

/**
 * 创建分类
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const createCategory = async (req, res) => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ success: false, message: '分类名称不能为空' });
    }
    
    // 检查是否存在同名分类
    const existingCategory = await Category.findOne({ where: { name } });
    if (existingCategory) {
      return res.status(400).json({ success: false, message: '分类名称已存在' });
    }
    
    // 创建分类
    const category = await Category.create({
      name,
      description: description || ''
    });
    
    return res.json({
      success: true,
      message: '分类创建成功',
      data: { category }
    });
  } catch (error) {
    console.error('创建分类错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取分类列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getCategories = async (req, res) => {
  try {
    // 获取所有分类，并包含关联的文件
    const categories = await Category.findAll({
      include: [
        {
          model: File,
          attributes: ['id']
        }
      ],
      order: [['create_time', 'DESC']]
    });
    
    return res.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 根据ID获取分类详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询分类
    const category = await Category.findByPk(id, {
      include: [
        {
          model: File,
          attributes: ['id', 'file_name']
        }
      ]
    });
    
    if (!category) {
      return res.status(404).json({ success: false, message: '分类不存在' });
    }
    
    return res.json({
      success: true,
      data: { category }
    });
  } catch (error) {
    console.error('获取分类详情错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 更新分类
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    
    // 查询分类
    const category = await Category.findByPk(id);
    
    if (!category) {
      return res.status(404).json({ success: false, message: '分类不存在' });
    }
    
    // 如果更改了名称，检查是否存在同名分类
    if (name && name !== category.name) {
      const existingCategory = await Category.findOne({ where: { name } });
      if (existingCategory) {
        return res.status(400).json({ success: false, message: '分类名称已存在' });
      }
    }
    
    // 更新分类
    await category.update({
      name: name || category.name,
      description: description !== undefined ? description : category.description
    });
    
    return res.json({
      success: true,
      message: '分类更新成功',
      data: { category }
    });
  } catch (error) {
    console.error('更新分类错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 删除分类
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询分类
    const category = await Category.findByPk(id);
    
    if (!category) {
      return res.status(404).json({ success: false, message: '分类不存在' });
    }
    
    // 检查分类下是否有文件
    const fileCount = await File.count({ where: { category_id: id } });
    if (fileCount > 0) {
      return res.status(400).json({ 
        success: false, 
        message: `该分类下有${fileCount}个文件，无法删除` 
      });
    }
    
    // 删除分类
    await category.destroy();
    
    return res.json({
      success: true,
      message: '分类删除成功'
    });
  } catch (error) {
    console.error('删除分类错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

module.exports = {
  createCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  deleteCategory
}; 