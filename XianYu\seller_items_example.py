#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼卖家商品列表获取示例 - 增强版

功能：
1. 获取卖家商品列表
2. 可选择获取每个商品的完整描述和所有图片链接
3. 对比列表描述和详情描述的差异
4. 保存完整数据到文件

使用方法：
1. 登录闲鱼网页版 https://www.goofish.com
2. 打开浏览器开发者工具(F12)
3. 在Network标签页中找到任意请求
4. 复制Request Headers中的Cookie值
5. 将Cookie值填入下面的cookies_str变量中
6. 设置 get_full_details = True 获取完整描述，False 只获取列表信息
7. 运行此脚本

注意：
- get_full_details=True 时会为每个商品调用详情API，速度较慢但信息完整
- get_full_details=False 时只显示列表信息，速度快但描述可能不完整
"""

import json
import time
from XianyuApis import XianyuApis
from utils.xianyu_utils import trans_cookies, generate_device_id


def main():
    # ========== 配置区域 ==========
    # 请在这里填入您的完整cookie字符串
    cookies_str = r't=d0de6f288ecdcae2d921bb2ea5b3f43f; cna=7LDpIPwbyV8CAd9ocIqBEj7s; tracknick=%E9%97%AB%E7%A4%BC%E8%B4%A4%EF%BC%81; havana_lgc2_77=eyJoaWQiOjIyMDA2ODUzMTgwNjAsInNnIjoiZDlhNTUzMTZlMmU3YWVjNzlmYTQyMmRmNTQ3ZTk2YzgiLCJzaXRlIjo3NywidG9rZW4iOiIxdzNTV0VFLTZvbFhUaXVBbU5yS2N5QSJ9; _hvn_lgc_=77; havana_lgc_exp=1783340082649; isg=BDMz5jUYX6txGRNsF-3tPhf9wjddaMcqP7dlteXQzdKJ5FOGbTqXeyh_mhQKxB8i; unb=2200685318060; cookie2=2e6a16aad94fc08c4c580509125fd074; xlly_s=1; _samesite_flag_=true; sgcookie=E100ija5ybq%2BssRMoPALCOe20iBQXtqoubCLcXsjCI8sL0aStWdOBQ4Gwv69RmVtR07t1T2Zcgz%2FTfUnsG8l%2F33n04cwljjZ2LDrvTaXRdiWoac%3D; csg=c2abe422; _tb_token_=e514365f5ab85; sdkSilent=1754219076518; mtop_partitioned_detect=1; _m_h5_tk=be495686fa4931fc374acb3c29d913bb_1754176198385; _m_h5_tk_enc=9e6466ac997015cda0aadf45a8069fa9; x5sec=7b22733b32223a2230313938613835663337366139396366222c22617365727665723b33223a22307c434b483575635147454b4b4c2f496748476738794d6a41774e6a67314d7a45344d4459774f7a497771716e646d766a2f2f2f2f2f41513d3d227d; tfstk=g4WoQlscF_RW7hDSZkv7NZo0r0VxPL9BgwHpJpLUgE8jy4HRY2bhuNI-e9Ie-wbvlgHLVgn5GNIZeTFWpvvWdpzTWReTVg9BL9CCOD850itUD3l0NkgWdpzTkjHeNRvCzCVR5BS4mHKKUY7eYnk2lHxEaw8ygq-Mfp8F8w823h-oTb-yaI82lHJe8wJUmK8Xxp8F8prc0xBLaU6F3tzhjob6Y9GhQ3Ak7g8P2gB4ZQG54E44utfDZFzHovkFn3j3r8uiIWLN1Ud9GZyxEpj2xaLA7RkD3Cf1vHbuEYJRgs6WetUifQbHyCWDi5oDK_7lTC10CfOytUfVUImELi9VgC5hrzcWz_QD1nRqxRtAWES5UsqIug79rL-2MRrG0B5AFC68uYvN1gpRTwNtGLjFaKjzfjlZRPkB0kBqOXOycnY9dTWn3bsqGFr0mfrBantWWoqmOXOycnYTmocadQ-XVFC..'
    
    # 可选：指定要查看的卖家ID，如果为空则查看自己的商品
    target_seller_id = None  # 例如: "2202640918079"
    
    # 分页设置
    page_num = 1      # 页码，从1开始
    page_size = 20    # 每页商品数量（闲鱼API最大支持20）

    # 是否获取完整商品描述和图片
    get_full_details = True   # True=获取完整描述和所有图片，False=只显示列表信息

    # 详情获取设置
    detail_delay = 2          # 每个商品详情获取的延迟时间（秒）
    max_detail_items = 0      # 最多获取多少个商品的详情（0=全部，建议先用5测试）

    # 注意：get_full_details=True 时会调用详情API
    # 如果出现 FAIL_SYS_USER_VALIDATE 错误，请更新Cookie或减少请求频率

    # 是否获取所有页面的商品
    get_all_pages = True  # True=自动获取所有页面，False=只获取第一页
    # ========== 配置区域结束 ==========
    
    if not cookies_str.strip():
        print("❌ 错误：请先设置cookies_str")
        print("\n📖 Cookie获取步骤：")
        print("1. 打开浏览器，访问 https://www.goofish.com")
        print("2. 登录您的闲鱼账号")
        print("3. 按F12打开开发者工具")
        print("4. 切换到Network(网络)标签页")
        print("5. 刷新页面或点击任意链接")
        print("6. 在请求列表中选择任意一个请求")
        print("7. 在Request Headers中找到Cookie字段")
        print("8. 复制完整的Cookie值到上面的cookies_str变量中")
        return
    
    print("🚀 开始获取闲鱼商品列表...")
    
    try:
        # 初始化API
        xianyu = XianyuApis()
        
        # 解析cookies
        cookies = trans_cookies(cookies_str)
        print("✅ Cookie解析成功")
        
        # 获取用户ID
        user_id = cookies.get('unb', '')
        if not user_id:
            print("❌ 错误：Cookie中未找到用户ID(unb字段)")
            print("请确保Cookie是完整的，包含unb字段")
            return
        
        print(f"👤 当前用户ID: {user_id}")
        
        # 生成设备ID
        device_id = generate_device_id(user_id)
        print(f"📱 设备ID: {device_id}")
        
        # 确定查看的卖家
        if target_seller_id:
            print(f"🎯 查看卖家 {target_seller_id} 的商品")
        else:
            print("🏠 查看自己的商品")
        
        if get_all_pages:
            print(f"📄 自动获取所有页面的商品（每页最多{page_size}个）")
        else:
            print(f"📄 获取第{page_num}页，每页{page_size}个商品")
        print("-" * 50)

        # 获取商品列表（支持自动分页）
        all_items = []
        current_page = page_num
        total_count = 0

        while True:
            print(f"🔄 正在获取第{current_page}页...")

            response = xianyu.get_seller_items(
                cookies=cookies,
                device_id=device_id,
                seller_id=target_seller_id,
                page_num=current_page,
                page_size=page_size
            )

            parsed_result = xianyu.parse_seller_items(response)

            if not parsed_result['success']:
                print(f"❌ 第{current_page}页获取失败: {parsed_result['message']}")
                break

            page_items = parsed_result['data']['items']
            if not page_items:
                print(f"📭 第{current_page}页没有商品")
                break

            all_items.extend(page_items)
            total_count = parsed_result['data']['total']

            print(f"✅ 第{current_page}页获取到 {len(page_items)} 个商品")
            print(f"📊 累计获取: {len(all_items)} 个商品")

            # 检查是否需要继续获取下一页
            if not get_all_pages:
                # 只获取指定页面
                break
            elif not parsed_result['data']['hasMore']:
                # 没有更多页面了
                print(f"📄 已获取所有页面")
                break
            else:
                # 继续获取下一页
                current_page += 1
                time.sleep(0.5)  # 添加延迟避免请求过快

        # 构造最终结果
        if all_items:
            items = all_items
            total = len(all_items)

            print(f"\n📊 获取完成！")
            print(f"   实际商品总数: {total}")
            print(f"   获取页数: {current_page - page_num + 1}")
            print(f"   平均每页: {total/(current_page - page_num + 1):.1f} 个商品")

            if items:
                if get_full_details:
                    # 确定要获取详情的商品数量
                    detail_count = len(items) if max_detail_items == 0 else min(max_detail_items, len(items))

                    print(f"\n📦 获取完整商品详情...")
                    print(f"   将获取前 {detail_count} 个商品的详细信息")
                    print(f"   每个商品间隔 {detail_delay} 秒")
                    print("=" * 100)

                    enhanced_items = []
                    success_count = 0

                    for i, item in enumerate(items, 1):
                        # 检查是否超过限制
                        if max_detail_items > 0 and i > max_detail_items:
                            print(f"\n⏹️ 已达到详情获取限制 ({max_detail_items} 个)，跳过剩余商品")
                            # 将剩余商品添加到列表中（不获取详情）
                            enhanced_items.extend(items[i-1:])
                            break

                        print(f"\n🔄 处理第 {i}/{detail_count} 个商品: {item['title'][:50]}...")

                        try:
                            # 获取商品详情
                            detail_response = xianyu.get_item_detail(
                                cookies=cookies,
                                device_id=device_id,
                                item_id=item['itemId']
                            )

                            detail_result = xianyu.parse_item_detail(detail_response)

                            if detail_result['success']:
                                detail_data = detail_result['data']

                                print(f"✅ 详情获取成功")

                                # 显示完整商品信息
                                print(f"\n{'='*80}")
                                print(f"🏷️ 商品 {i}: {detail_data['title']}")
                                # 优先使用原始商品列表中的itemId，如果详情中没有的话
                                item_id = detail_data['itemId'] or item['itemId']
                                print(f"🆔 ID: {item_id}")
                                print(f"� 价格: {detail_data['price']}")
                                print(f"📊 状态: {detail_data['statusText']}")

                                # 显示完整描述
                                desc = detail_data['desc']
                                if desc:
                                    print(f"\n📄 完整商品描述 (共{len(desc)}字符):")
                                    print("─" * 80)
                                    print(desc)
                                    print("─" * 80)
                                else:
                                    print(f"\n📄 商品描述: 无")

                                # 显示所有图片
                                if detail_data['images']:
                                    print(f"\n🖼️ 商品图片 (共{len(detail_data['images'])}张):")
                                    print("─" * 80)
                                    for j, img in enumerate(detail_data['images'], 1):
                                        print(f"图片{j:2d}: {img}")
                                    print("─" * 80)
                                else:
                                    print(f"\n🖼️ 商品图片: 无")

                                # 其他信息
                                if detail_data.get('tags'):
                                    print(f"\n�️ 标签: {detail_data['tags']}")

                                # 保存增强后的商品信息
                                enhanced_item = item.copy()
                                enhanced_item['fullDescription'] = desc
                                enhanced_item['detailImages'] = detail_data['images']
                                enhanced_item['hasDetailDesc'] = bool(desc)
                                enhanced_item['descImproved'] = bool(desc) and desc != item['description']
                                enhanced_items.append(enhanced_item)

                                print(f"\n✅ 第 {i} 个商品处理完成")
                                success_count += 1

                            else:
                                print(f"❌ 详情获取失败: {detail_result['message']}")
                                if "FAIL_SYS_USER_VALIDATE" in detail_result['message']:
                                    print("   💡 提示：用户验证失败，可能需要更新Cookie或减少请求频率")
                                enhanced_items.append(item)

                            # 添加延迟避免请求过快
                            if i < detail_count:
                                print(f"⏳ 等待 {detail_delay} 秒...")
                                time.sleep(detail_delay)

                        except Exception as e:
                            print(f"❌ 处理异常: {e}")
                            enhanced_items.append(item)

                    # 保存增强后的数据
                    output_file = f"enhanced_seller_items_page_{page_num}.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(enhanced_items, f, ensure_ascii=False, indent=2)
                    print(f"\n💾 增强数据已保存到: {output_file}")

                    # 统计信息
                    total_items = len(enhanced_items)
                    items_with_detail = sum(1 for item in enhanced_items if item.get('hasDetailDesc', False))
                    improved_desc = sum(1 for item in enhanced_items if item.get('descImproved', False))

                    print(f"\n📈 详情获取统计:")
                    print(f"   总商品数: {total_items}")
                    print(f"   尝试获取详情: {detail_count}")
                    print(f"   成功获取详情: {success_count}")
                    print(f"   获取到完整描述: {items_with_detail}")
                    print(f"   描述得到改善: {improved_desc}")
                    print(f"   详情获取成功率: {success_count/detail_count*100:.1f}%")

                    if success_count > 0:
                        print(f"\n✅ 成功获取了 {success_count} 个商品的完整描述和图片！")
                    else:
                        print(f"\n⚠️ 未能获取到任何商品的详情，建议检查Cookie或减少请求频率")

                else:
                    # 只显示列表信息（原有逻辑）
                    print(f"\n📦 商品列表:")
                    print("=" * 80)

                    for i, item in enumerate(items, 1):
                        print(f"\n🏷️  商品 {i}:")
                        print(f"   📝 标题: {item['title']}")
                        print(f"   🆔 ID: {item['itemId']}")
                        print(f"   � 价格: {item['price']}")

                        if item['originalPrice']:
                            print(f"   💸 原价: {item['originalPrice']}")

                        print(f"   📊 状态: {item['statusText'] or item['status']}")
                        print(f"   👀 浏览: {item['viewCount']}")
                        print(f"   ❤️  点赞: {item['likeCount']}")
                        print(f"   💬 评论: {item['commentCount']}")

                        if item['publishTime']:
                            print(f"   📅 发布: {item['publishTime']}")

                        if item['location']:
                            print(f"   📍 地区: {item['location']}")

                        if item['category']:
                            print(f"   🏷️  分类: {item['category']}")

                        if item['mainImage']:
                            print(f"   🖼️  主图: {item['mainImage']}")

                        if item['description']:
                            desc = item['description'][:100] + "..." if len(item['description']) > 100 else item['description']
                            print(f"   📄 描述: {desc}")

                        print("-" * 40)

                    # 保存列表数据到文件
                    output_file = f"seller_items_page_{page_num}.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(parsed_result, f, ensure_ascii=False, indent=2)
                    print(f"\n💾 列表数据已保存到: {output_file}")
                
            else:
                print("\n📭 当前没有商品")
        else:
            print("\n� 没有获取到任何商品")
            
    except KeyError as e:
        print(f"❌ Cookie格式错误，缺少必要字段: {e}")
        print("请确保Cookie包含_m_h5_tk和unb字段")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
