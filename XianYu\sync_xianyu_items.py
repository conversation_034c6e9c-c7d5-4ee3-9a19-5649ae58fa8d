#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼商品同步脚本 - 基于seller_items_example.py增强版
用于将闲鱼商品信息同步到数据库中

功能：
1. 获取卖家商品列表
2. 获取每个商品的完整描述和所有图片链接
3. 输出JSON格式结果供后端调用

使用方法：
python sync_xianyu_items.py <cookies_str> [max_items]
"""

import json
import sys
import time
import io
from XianyuApis import XianyuApis
from utils.xianyu_utils import trans_cookies, generate_device_id

# 设置标准输出编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')


def sync_xianyu_items(cookies_str, max_items=50):
    """
    同步闲鱼商品信息 - 基于seller_items_example.py的增强版

    Args:
        cookies_str: 闲鱼Cookie字符串
        max_items: 最大同步商品数量

    Returns:
        dict: 同步结果
    """
    result = {
        'success': False,
        'message': '',
        'data': {
            'items': [],
            'total': 0,
            'synced': 0,
            'errors': []
        }
    }

    try:
        if not cookies_str.strip():
            result['message'] = '请提供有效的Cookie字符串'
            return result

        # 初始化API
        xianyu = XianyuApis()

        # 解析cookies
        cookies = trans_cookies(cookies_str)

        # 获取用户ID
        user_id = cookies.get('unb', '')
        if not user_id:
            result['message'] = 'Cookie中未找到用户ID(unb字段)'
            return result

        # 生成设备ID
        device_id = generate_device_id(user_id)

        # 获取商品列表（支持自动分页）
        all_items = []
        current_page = 1
        page_size = 20

        while len(all_items) < max_items:
            # 获取当前页商品
            response = xianyu.get_seller_items(
                cookies=cookies,
                device_id=device_id,
                seller_id=None,  # 获取自己的商品
                page_num=current_page,
                page_size=page_size
            )

            parsed_result = xianyu.parse_seller_items(response)

            if not parsed_result['success']:
                result['message'] = f'获取商品列表失败: {parsed_result["message"]}'
                return result

            page_items = parsed_result['data']['items']
            if not page_items:
                break  # 没有更多商品

            all_items.extend(page_items)

            # 检查是否还有更多页面
            if not parsed_result['data']['hasMore']:
                break

            current_page += 1
            time.sleep(0.5)  # 避免请求过快

        # 限制商品数量
        if len(all_items) > max_items:
            all_items = all_items[:max_items]

        # 获取商品详情（使用seller_items_example.py的增强逻辑）
        enhanced_items = []
        success_count = 0
        detail_delay = 1  # 每个商品详情获取的延迟时间（秒）

        for i, item in enumerate(all_items):
            try:
                # 获取商品详情
                detail_response = xianyu.get_item_detail(
                    cookies=cookies,
                    device_id=device_id,
                    item_id=item['itemId']
                )

                detail_result = xianyu.parse_item_detail(detail_response)

                if detail_result['success']:
                    detail_data = detail_result['data']

                    # 使用seller_items_example.py的数据合并逻辑
                    enhanced_item = item.copy()
                    enhanced_item['fullDescription'] = detail_data['desc']
                    enhanced_item['detailImages'] = detail_data['images']
                    enhanced_item['hasDetailDesc'] = bool(detail_data['desc'])
                    enhanced_item['descImproved'] = bool(detail_data['desc']) and detail_data['desc'] != item['description']

                    # 为了兼容后端，保持原有字段结构
                    enhanced_item['description'] = detail_data['desc'] or item['description']
                    enhanced_item['images'] = detail_data['images'] or item['images'] or []
                    enhanced_item['title'] = detail_data['title'] or item['title']
                    enhanced_item['price'] = detail_data['price'] or item['price']
                    enhanced_item['mainImage'] = detail_data['mainImage'] or item['mainImage']

                    enhanced_items.append(enhanced_item)
                    success_count += 1

                else:
                    # 如果获取详情失败，使用基本信息
                    enhanced_item = {
                        'itemId': item['itemId'],
                        'title': item['title'],
                        'description': item['description'],
                        'price': item['price'],
                        'images': item['images'] or [],
                        'mainImage': item['mainImage'],
                        'status': item['status'],
                        'statusText': item['statusText'],
                        'fullDescription': item['description'],
                        'detailImages': item['images'] or [],
                        'hasDetailDesc': False,
                        'descImproved': False
                    }
                    enhanced_items.append(enhanced_item)
                    result['data']['errors'].append(f"商品 {item['itemId']} 详情获取失败")

                # 添加延迟避免请求过快
                if i < len(all_items) - 1:
                    time.sleep(detail_delay)

            except Exception as e:
                result['data']['errors'].append(f"处理商品 {item['itemId']} 时出错: {str(e)}")
                # 即使出错也要添加基本信息
                enhanced_item = {
                    'itemId': item['itemId'],
                    'title': item['title'],
                    'description': item['description'],
                    'price': item['price'],
                    'images': item['images'] or [],
                    'mainImage': item['mainImage'],
                    'status': item['status'],
                    'statusText': item['statusText'],
                    'fullDescription': item['description'],
                    'detailImages': item['images'] or [],
                    'hasDetailDesc': False,
                    'descImproved': False
                }
                enhanced_items.append(enhanced_item)
                continue

        result['success'] = True
        result['message'] = '同步完成'
        result['data']['items'] = enhanced_items
        result['data']['total'] = len(all_items)
        result['data']['synced'] = len(enhanced_items)
        result['data']['detail_success_count'] = success_count

        return result

    except Exception as e:
        result['message'] = f'同步过程中出错: {str(e)}'
        return result


def main():
    """命令行入口"""
    if len(sys.argv) < 2:
        print(json.dumps({
            'success': False,
            'message': '请提供Cookie字符串作为参数'
        }, ensure_ascii=False))
        return
    
    cookies_str = sys.argv[1]
    max_items = int(sys.argv[2]) if len(sys.argv) > 2 else 50
    
    result = sync_xianyu_items(cookies_str, max_items)
    print(json.dumps(result, ensure_ascii=False))


if __name__ == '__main__':
    main()
