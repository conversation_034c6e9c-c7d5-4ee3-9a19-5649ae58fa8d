import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import fileReducer from './slices/fileSlice';
import categoryReducer from './slices/categorySlice';
import adminReducer from './slices/adminSlice';

// 配置 store
export const store = configureStore({
  reducer: {
    auth: authReducer,
    file: fileReducer,
    category: categoryReducer,
    admin: adminReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略非序列化字段的检查
        ignoredActions: ['payload'],
        ignoredActionPaths: ['meta.arg', 'payload.timestamp'],
        ignoredPaths: ['items.dates']
      }
    })
});

export default store; 