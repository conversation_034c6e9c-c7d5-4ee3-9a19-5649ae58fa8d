import React, { useEffect, useState, useCallback } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  InputNumber,
  Input,
  Select,
  message,
  Typography,
  Spin,
  Tag,
  Tooltip,
  Popconfirm,
  Alert,
  Badge,
  Radio,
  Divider,
  Row,
  Col,
  App,
  DatePicker
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  ExportOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  FilterOutlined,
  ClearOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import {
  getCardKeys,
  generateKeys,
  updateCardKeyStatus,
  deleteCardKey,
  batchDeleteCardKeys,
  cleanupExpiredCardKeys,
  cleanupUsedCardKeys,
  cleanupInvalidCardKeys,
  clearGeneratedKeys,
  setKeyPage,
  setSelectedRowKeys,
  clearAdminError
} from '../../store/slices/adminSlice';
import moment from 'moment';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 导出卡密列表为文本文件
const exportKeys = (keys) => {
  const content = keys.map(key => key.key_code).join('\n');
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `卡密列表_${new Date().toISOString().slice(0, 10)}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

const CardKeys = () => {
  const [generateForm] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [generateModalVisible, setGenerateModalVisible] = useState(false);
  const [generatedKeysModalVisible, setGeneratedKeysModalVisible] = useState(false);
  const [filterStatus, setFilterStatus] = useState(null);
  const [filterIsUsed, setFilterIsUsed] = useState(null);
  const { message: messageApi } = App.useApp();

  const dispatch = useDispatch();
  const {
    cardKeys,
    generatedKeys,
    totalKeys,
    keyPage,
    keyLimit,
    loadingKeys,
    generatingKeys,
    error,
    selectedRowKeys
  } = useSelector((state) => state.admin);

  // 初始加载卡密列表
  useEffect(() => {
    dispatch(getCardKeys({ page: 1, limit: 10 }));

    // 清除错误
    return () => {
      dispatch(clearAdminError());
    };
  }, [dispatch]);

  // 表单重置函数
  const resetFilterForm = useCallback(() => {
    // 直接使用resetFields而不是setFieldValue，避免循环引用
    filterForm.resetFields(['is_used']);
  }, []); // 不依赖任何状态，保持稳定引用

  // 添加一个备选的复制函数
  const fallbackCopyTextToClipboard = (text) => {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      
      // 设置不可见
      textArea.style.position = 'fixed';
      textArea.style.top = '0';
      textArea.style.left = '0';
      textArea.style.width = '2em';
      textArea.style.height = '2em';
      textArea.style.padding = '0';
      textArea.style.border = 'none';
      textArea.style.outline = 'none';
      textArea.style.boxShadow = 'none';
      textArea.style.background = 'transparent';
      
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (successful) {
        messageApi.success('已复制所有卡密');
      } else {
        messageApi.error('复制失败，请手动复制');
      }
    } catch (err) {
      console.error('复制失败:', err);
      messageApi.error('复制失败，请手动复制');
    }
  };

  // 定义表格列
  const columns = [
    {
      title: '卡密',
      dataIndex: 'key_code',
      key: 'key_code',
      width: 310,
      render: (text) => <Typography.Text copyable>{text}</Typography.Text>
    },
    {
      title: '类型',
      dataIndex: 'key_type',
      key: 'key_type',
      width: 50,
      render: (type) => (
        <Tag color={type === 'multi' ? 'blue' : 'default'}>
          {type === 'multi' ? '多设备' : '单设备'}
        </Tag>
      )
    },
    {
      title: '使用情况',
      key: 'devices',
      width: 120,
      render: (_, record) => {
        if (record.key_type === 'multi') {
          let usedCount = 0;
          if (record.used_devices) {
            try {
              // 检查是否已经是对象，避免重复解析
              const devices = typeof record.used_devices === 'string' ? 
                JSON.parse(record.used_devices) : record.used_devices;
              
              // 避免直接使用devices对象，只获取长度
              usedCount = Array.isArray(devices) ? devices.length : 0;
            } catch (err) {
              console.error('解析已使用设备列表错误:', err);
            }
          }
          return `${usedCount}/${record.max_devices}`;
        }
        return '-';
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 60,
      render: (status, record) => {
        // 过期状态检查
        const isExpired = record.expire_time && new Date(record.expire_time) < new Date();
        
        if (isExpired) {
          return <Tag color="red">已过期</Tag>;
        }
        
        return (
          <Tag color={status ? 'green' : 'red'}>
            {status ? '有效' : '无效'}
          </Tag>
        );
      }
    },
    {
      title: '使用状态',
      dataIndex: 'is_used',
      key: 'is_used',
      render: (isUsed, record) => {
        if (record.key_type === 'multi') {
          let usedCount = 0;
          if (record.used_devices) {
            try {
              // 检查是否已经是对象，避免重复解析
              const devices = typeof record.used_devices === 'string' ? 
                JSON.parse(record.used_devices) : record.used_devices;
              
              // 避免直接使用devices对象，只获取长度
              usedCount = Array.isArray(devices) ? devices.length : 0;
            } catch (err) {
              console.error('解析已使用设备列表错误:', err);
            }
          }
          
          if (usedCount === 0) {
            return <Tag color="green">未使用</Tag>;
          } else if (usedCount < record.max_devices) {
            return <Tag color="orange">部分使用</Tag>;
          } else {
            return <Tag color="red">已用完</Tag>;
          }
        } else {
          return (
            <Tag color={isUsed ? 'red' : 'green'}>
              {isUsed ? '已使用' : '未使用'}
            </Tag>
          );
        }
      }
    },
    {
      title: '已提取文件',
      dataIndex: 'usedFile',
      key: 'usedFile',
      width: 80,
      render: (usedFile) => usedFile ? (
        <Tooltip title={usedFile.file_name}>
          <Typography.Text ellipsis style={{ maxWidth: 70 }}>
            {usedFile.file_name}
          </Typography.Text>
        </Tooltip>
      ) : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 120,
      render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '使用时间',
      dataIndex: 'used_time',
      key: 'used_time',
      width: 120,
      render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '过期时间',
      dataIndex: 'expire_time',
      key: 'expire_time',
      width: 180,
      render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '永久有效'
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space>
          <Popconfirm
            title={`确定要${record.status ? '禁用' : '启用'}该卡密吗？`}
            onConfirm={() => handleToggleStatus(record.id, !record.status)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              size="small"
              icon={record.status ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            >
              {record.status ? '禁用' : '启用'}
            </Button>
          </Popconfirm>
          
          {renderDeleteButton(record)}
        </Space>
      )
    }
  ];

  // 处理启用/禁用卡密状态
  const handleToggleStatus = (id, status) => {
    dispatch(updateCardKeyStatus({ id, status }))
      .unwrap()
      .then(() => {
        messageApi.success(status ? '卡密已启用' : '卡密已禁用');
      })
      .catch((err) => {
        messageApi.error(`操作失败: ${err}`);
      });
  };

  // 生成卡密弹窗
  const renderGenerateModal = () => (
    <Modal
      title="生成卡密"
      open={generateModalVisible}
      onOk={() => {
        generateForm
          .validateFields()
          .then(values => {
            handleGenerate(values);
            generateForm.resetFields();
          })
          .catch(info => {
            console.log('验证失败:', info);
          });
      }}
      onCancel={() => {
        setGenerateModalVisible(false);
        generateForm.resetFields();
      }}
      okText="生成"
      cancelText="取消"
      confirmLoading={generatingKeys}
    >
      <Form
        form={generateForm}
        layout="vertical"
        initialValues={{ count: 1, keyType: 'single', maxDevices: 1 }}
      >
        <Form.Item
          name="count"
          label="生成数量"
          rules={[{ required: true, message: '请输入生成数量' }]}
        >
          <InputNumber min={1} max={100} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item name="prefix" label="卡密前缀">
          <Input placeholder="选填，卡密将以此为前缀" />
        </Form.Item>

        <Form.Item name="expireDays" label="有效期(天)">
          <InputNumber min={1} style={{ width: '100%' }} placeholder="选填，不填则永久有效" />
        </Form.Item>

        <Form.Item name="keyType" label="卡密类型">
          <Radio.Group>
            <Radio value="single">单设备卡密</Radio>
            <Radio value="multi">多设备卡密</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.keyType !== currentValues.keyType}
        >
          {({ getFieldValue }) => 
            getFieldValue('keyType') === 'multi' ? (
              <Form.Item 
                name="maxDevices" 
                label="最大设备数" 
                rules={[{ required: true, message: '请输入最大设备数' }]}
              >
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
            ) : null
          }
        </Form.Item>
      </Form>
    </Modal>
  );

  // 处理卡密生成
  const handleGenerate = (values) => {
    const { count, prefix, expireDays, keyType, maxDevices } = values;
    dispatch(generateKeys({ count, prefix, expireDays, keyType, maxDevices }));
    setGenerateModalVisible(false);
    setGeneratedKeysModalVisible(true);
  };

  // 关闭生成的卡密列表弹窗
  const handleCloseGeneratedKeys = () => {
    setGeneratedKeysModalVisible(false);
    dispatch(clearGeneratedKeys());
  };

  // 处理分页变化
  const handlePageChange = (page, pageSize) => {
    dispatch(setKeyPage(page));
    dispatch(getCardKeys({ 
      page, 
      limit: pageSize,
      is_used: filterIsUsed,
      status: filterStatus
    }));
  };

  // 删除按钮渲染
  const renderDeleteButton = (record) => {
    return (
      <Popconfirm
        title="确定要删除该卡密吗？"
        description="删除后无法恢复"
        onConfirm={() => handleDeleteKey(record.id)}
        okText="确定"
        cancelText="取消"
        okButtonProps={{ danger: true }}
        icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
      >
        <Button 
          type="link" 
          size="small" 
          danger
          icon={<DeleteOutlined />}
        >
          删除
        </Button>
      </Popconfirm>
    );
  };

  // 处理删除卡密
  const handleDeleteKey = (id) => {
    dispatch(deleteCardKey(id))
      .unwrap()
      .then(({message}) => {
        messageApi.success(message || '卡密删除成功');
        
        // 删除成功后重新获取当前页数据
        const currentPage = cardKeys.length === 1 ? 1 : keyPage;
        
        // 更新状态
        setFilterIsUsed(null);
        
        // 重置表单字段
        resetFilterForm();
        
        // 获取新数据
        dispatch(getCardKeys({ 
          page: currentPage, 
          limit: keyLimit,
          status: filterStatus 
        }));
      })
      .catch((err) => {
        messageApi.error(`删除失败: ${err}`);
      });
  };

  // 处理批量删除卡密
  const handleBatchDelete = () => {
    if (!selectedRowKeys.length) {
      messageApi.warning('请先选择要删除的卡密');
      return;
    }

    dispatch(batchDeleteCardKeys(selectedRowKeys))
      .unwrap()
      .then(({message}) => {
        messageApi.success(message || '卡密批量删除成功');
        
        // 计算删除后应该显示的页码
        const currentItems = cardKeys.length;
        const selectedCount = selectedRowKeys.length;
        
        // 如果当前页的所有数据都被删除了，则回到前一页（或第一页）
        let newPage = keyPage;
        if (currentItems === selectedCount) {
          newPage = Math.max(1, keyPage - 1);
        }
        
        // 更新状态
        setFilterIsUsed(null);
        
        // 重置表单字段
        resetFilterForm();
        
        // 获取新数据
        dispatch(getCardKeys({
          page: newPage,
          limit: keyLimit,
          status: filterStatus
        }));
      })
      .catch((err) => {
        messageApi.error(`批量删除失败: ${err}`);
      });
  };

  // 清理过期卡密
  const handleCleanupExpiredKeys = () => {
    dispatch(cleanupExpiredCardKeys())
      .unwrap()
      .then(({message}) => {
        messageApi.success(message || '已清理过期卡密');
        // 清理成功后重新获取当前页数据
        const currentPage = cardKeys.length === 1 ? 1 : keyPage;
        setFilterIsUsed(null);
        resetFilterForm();
        dispatch(getCardKeys({
          page: currentPage,
          limit: keyLimit,
          status: filterStatus
        }));
      })
      .catch((err) => {
        messageApi.error(`清理过期卡密失败: ${err}`);
      });
  };

  // 清理已使用卡密
  const handleCleanupUsedKeys = () => {
    dispatch(cleanupUsedCardKeys())
      .unwrap()
      .then(({message}) => {
        messageApi.success(message || '已清理已使用卡密');
        // 清理成功后重新获取当前页数据
        const currentPage = cardKeys.length === 1 ? 1 : keyPage;
        setFilterIsUsed(null);
        resetFilterForm();
        dispatch(getCardKeys({
          page: currentPage,
          limit: keyLimit,
          status: filterStatus
        }));
      })
      .catch((err) => {
        messageApi.error(`清理已使用卡密失败: ${err}`);
      });
  };

  // 清理无效卡密
  const handleCleanupInvalidKeys = () => {
    dispatch(cleanupInvalidCardKeys())
      .unwrap()
      .then(({message}) => {
        messageApi.success(message || '已清理无效卡密');
        // 清理成功后重新获取当前页数据
        const currentPage = cardKeys.length === 1 ? 1 : keyPage;
        setFilterIsUsed(null);
        resetFilterForm();
        dispatch(getCardKeys({
          page: currentPage,
          limit: keyLimit,
          status: filterStatus
        }));
      })
      .catch((err) => {
        messageApi.error(`清理无效卡密失败: ${err}`);
      });
  };

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys) => {
      dispatch(setSelectedRowKeys(keys));
    }
  };

  // 处理筛选
  const handleFilter = (values) => {
    const { is_used, status } = values;
    setFilterIsUsed(is_used);
    setFilterStatus(status);
    
    dispatch(getCardKeys({ 
      page: 1, 
      limit: keyLimit,
      is_used,
      status
    }));
  };

  // 重置筛选
  const handleResetFilter = () => {
    filterForm.resetFields();
    setFilterIsUsed(null);
    setFilterStatus(null);
    dispatch(getCardKeys({ page: 1, limit: keyLimit }));
  };

  return (
    <div>
      <Title level={2}>卡密管理</Title>
      <Text type="secondary" style={{ marginBottom: 20, display: 'block' }}>
        管理系统中的所有卡密，包括生成、查看和删除等操作。
      </Text>

      {error && (
        <Alert
          message="操作失败"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: 16 }}
          onClose={() => dispatch(clearAdminError())}
        />
      )}

      <Card style={{ marginBottom: 16 }}>
        <Form
          form={filterForm}
          layout="inline"
          onFinish={handleFilter}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="is_used" label="使用状态">
            <Select style={{ width: 120 }} allowClear placeholder="全部">
              <Option value={false}>未使用</Option>
              <Option value={true}>已使用</Option>
            </Select>
          </Form.Item>

          <Form.Item name="status" label="卡密状态">
            <Select style={{ width: 120 }} allowClear placeholder="全部">
              <Option value={1}>正常</Option>
              <Option value={0}>禁用</Option>
              <Option value={2}>已过期</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              筛选
            </Button>
          </Form.Item>

          <Form.Item>
            <Button icon={<ReloadOutlined />} onClick={handleResetFilter}>
              重置
            </Button>
          </Form.Item>
        </Form>

        <Space style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setGenerateModalVisible(true)}
          >
            生成卡密
          </Button>

          {selectedRowKeys.length > 0 && (
            <Popconfirm
              title="批量删除卡密"
              description={`确定要删除选中的 ${selectedRowKeys.length} 个卡密吗？删除后无法恢复。`}
              onConfirm={handleBatchDelete}
              okText="确定"
              cancelText="取消"
              okButtonProps={{ danger: true }}
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            >
              <Button 
                danger 
                icon={<DeleteOutlined />}
              >
                批量删除
              </Button>
            </Popconfirm>
          )}

          <Button
            icon={<DownloadOutlined />}
            disabled={selectedRowKeys.length === 0}
            onClick={() => {
              const selectedKeys = cardKeys.filter(key => selectedRowKeys.includes(key.id));
              exportKeys(selectedKeys);
              messageApi.success('导出成功');
            }}
          >
            导出所选卡密
          </Button>

          <Popconfirm
            title="清理无效卡密"
            description="确定要清理所有已过期、已用完且已禁用的卡密吗？此操作不可恢复。"
            onConfirm={handleCleanupInvalidKeys}
            okText="确定"
            cancelText="取消"
            okButtonProps={{ danger: true }}
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              icon={<ClearOutlined />}
              danger
            >
              清理无效卡密
            </Button>
          </Popconfirm>
        </Space>

        {/* 显示选中数量 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Alert
              message={`已选择 ${selectedRowKeys.length} 个卡密`}
              type="info"
              showIcon
              action={
                <Button 
                  size="small" 
                  type="text"
                  onClick={() => dispatch(setSelectedRowKeys([]))}
                >
                  取消选择
                </Button>
              }
            />
          </div>
        )}

        <Spin spinning={loadingKeys}>
          <Table
            rowKey="id"
            rowSelection={rowSelection}
            columns={columns}
            dataSource={cardKeys}
            pagination={{
              current: keyPage,
              pageSize: keyLimit,
              total: totalKeys,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 个卡密`,
            }}
          />
        </Spin>
      </Card>

      {renderGenerateModal()}

      {/* 生成的卡密列表弹窗 */}
      <Modal
        title="生成的卡密列表"
        open={generatedKeysModalVisible}
        onCancel={handleCloseGeneratedKeys}
        width={600}
        footer={[
          <Button key="copy" onClick={() => {
            const content = generatedKeys.map(key => key.key_code).join('\n');
            
            // 添加兼容性检查
            if (navigator.clipboard && navigator.clipboard.writeText) {
              // 现代浏览器支持Clipboard API
              navigator.clipboard.writeText(content)
                .then(() => {
                  messageApi.success('已复制所有卡密');
                })
                .catch(err => {
                  console.error('复制失败:', err);
                  fallbackCopyTextToClipboard(content);
                });
            } else {
              // 不支持Clipboard API的浏览器使用备选方案
              fallbackCopyTextToClipboard(content);
            }
          }}>
            复制全部
          </Button>,
          <Button key="download" onClick={() => exportKeys(generatedKeys)}>
            导出为文本文件
          </Button>,
          <Button key="close" type="primary" onClick={handleCloseGeneratedKeys}>
            关闭
          </Button>
        ]}
      >
        <Alert
          message="请保存好这些卡密，关闭窗口后将无法再次查看完整卡密。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <div style={{ maxHeight: '300px', overflow: 'auto' }}>
          {generatedKeys.map((key, index) => (
            <div key={key.id} style={{ 
              display: 'flex', 
              justifyContent: 'space-between',
              padding: '8px',
              borderBottom: '1px solid #f0f0f0'
            }}>
              <Text copyable>{key.key_code}</Text>
              <span>#{index + 1}</span>
            </div>
          ))}
        </div>
      </Modal>
    </div>
  );
};

export default CardKeys; 