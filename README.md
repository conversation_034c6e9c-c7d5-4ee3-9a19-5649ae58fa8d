# 自助提取系统

自助提取系统是一个基于Web的应用程序，允许用户通过输入有效的卡密获取系统中的文件资料。系统支持一卡一提模式（每个卡密只能提取一个文件资料），并具备自动生成卡密、文件管理等完整功能，为资料分发提供安全、便捷的解决方案。

## 项目技术栈

### 前端技术
- **框架**：React.js 18.2.0
- **UI库**：Ant Design 5.12.5
- **状态管理**：Redux 5.0.1 + Redux Toolkit 2.0.1
- **路由**：React Router 6.21.1
- **HTTP客户端**：Axios 1.6.3
- **构建工具**：Webpack 5.89.0

### 后端技术
- **运行环境**：Node.js 18.19.0
- **Web框架**：Express 4.18.2
- **数据库**：MySQL 8.0.41
- **ORM**：Sequelize 6.35.2
- **身份验证**：jsonwebtoken 9.0.2
- **文件处理**：multer 1.4.5-lts.1
- **授权验证**：@supabase/supabase-js 2.39.3

## 功能模块

### 用户模块
- **卡密验证**：输入卡密后系统验证有效性，通过后展示可提取文件列表
- **文件浏览**：查看所有可提取的文件资料，支持分类浏览和搜索
- **文件提取**：查看文件详情并确认提取，一个卡密限提取一个文件
- **文件下载**：提供安全的文件下载链接，支持主流文件格式在线预览

### 管理模块
- **卡密管理**：自动生成卡密、导出卡密、查看使用状态等
- **文件管理**：上传、编辑、删除文件资料，支持文件分类
- **系统统计**：卡密使用情况、文件提取情况分析

### 授权模块
- **授权验证**：系统首次启动时要求输入授权码进行验证
- **设备绑定**：授权码与设备硬件指纹绑定，防止多设备使用同一授权码
- **记录追踪**：记录授权使用情况，支持授权状态查询
- **文件完整性验证**：关键文件进行哈希验证，防止代码被篡改

## 开发进度

### 2023年12月15日 - 项目启动
- 完成系统需求分析
- 设计系统整体架构
- 创建项目文档

### 2023年12月20日 - 数据库设计
- 完成数据库表结构设计
- 建立实体关系模型
- 设计API接口规范

### 2024年1月10日 - 后端开发
- 实现卡密生成与验证功能
- 完成文件上传与管理接口
- 建立API安全机制

### 2024年1月25日 - 前端开发
- 完成用户界面设计与实现
- 完成管理后台界面开发
- 实现前后端数据交互

### 2024年2月5日 - 系统测试
- 进行功能测试与安全测试
- 性能优化
- Bug修复

### 2024年2月15日 - 上线部署
- 完成系统部署
- 用户测试与反馈收集
- 系统正式上线运行

### 2024年7月20日 - 授权系统集成
- 实现基于Supabase的授权验证系统
- 添加设备绑定功能，防止授权码多设备使用
- 集成设备硬件指纹识别技术

### 2024年8月10日 - 安全性强化
- 添加文件完整性验证机制
- 对关键授权验证代码实施保护
- 防止代码被恶意修改或破解

## 安装与使用

### 系统要求
- Node.js v16+
- MySQL 8.0+
- Windows 10/11 或 Linux 服务器

### 安装步骤
1. 克隆项目仓库
```bash
git clone https://github.com/yourusername/zizhutihuoxitong.git
cd zizhutihuoxitong
```

2. 安装依赖
```bash
# 安装后端依赖
cd server
npm install

# 安装前端依赖
cd ../client
npm install
```

3. 配置数据库
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE zizhutihuoxitong CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -p zizhutihuoxitong < database/schema.sql
```

4. 配置环境变量
```bash
# 在server目录下创建.env文件
DB_HOST=localhost
DB_USER=root
DB_PASS=123456
DB_NAME=zizhutihuoxitong
JWT_SECRET=your_jwt_secret
PORT=5000
```

5. 初始化文件完整性验证
```bash
# 运行初始化脚本，生成哈希值
initialize-hashes.bat
```

6. 运行项目
```bash
# 使用启动脚本一键启动系统（含文件完整性验证）
start-system.bat

# 或者分别启动前后端服务
cd server
npm start

cd ../client
npm start
```

### 文件完整性保护
系统通过以下机制确保关键授权代码的完整性：

1. 首次部署系统后，需运行`initialize-hashes.bat`初始化文件哈希值
2. 运行`start-system.bat`启动系统时会自动验证文件完整性
3. 如果关键文件（尤其是授权相关代码）被修改，系统将无法启动
4. 更新系统后需重新运行`initialize-hashes.bat`更新哈希值

### 首次启动授权
- 系统首次启动时会要求输入授权码
- 授权码验证通过后会与当前设备绑定
- 后续启动不再需要输入授权码
- 授权码一经使用，不可在其他设备上再次使用

### 访问地址
- 用户界面: http://localhost:3000
- 管理后台: http://localhost:3000/admin
- API服务: http://localhost:5000/api

## 贡献指南
欢迎提交问题报告和功能建议。如果您希望贡献代码，请遵循以下步骤：
1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 提交 Pull Request

## 许可证
本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件

## 联系方式
- 开发者邮箱: <EMAIL>
- 项目仓库: [GitHub](https://github.com/yourusername/zizhutihuoxitong)

## 变更日志

### v1.3.0 (2024-08-10)
- 新增文件完整性验证功能
  - 关键授权验证代码加密保护
  - 文件哈希验证机制，防止代码被篡改
  - 代码被修改后系统将无法启动

### v1.2.0 (2024-07-20)
- 新增授权系统功能
  - 集成基于Supabase的授权验证
  - 实现设备绑定机制，一个授权码只能在一台设备上使用
  - 添加硬件指纹识别技术，防止未授权使用

### v1.1.0 (2024-07-10)
- 新增网盘链接上传功能
  - 管理员可直接上传纯文本信息，无需附带文件
  - 用户提取后显示文字内容而非下载按钮
  - 支持排版格式保留的文字展示

### v1.0.0 (2024-02-15)
- 初始版本发布
- 实现基础卡密验证功能
- 实现文件资料提取功能
- 完成管理后台基础功能 