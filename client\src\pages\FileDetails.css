/* 轮播图样式优化 */
.ant-carousel .slick-dots {
  bottom: 15px;
}

.ant-carousel .slick-dots li button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.ant-carousel .slick-dots li.slick-active button {
  background: #1890ff;
  border-color: #1890ff;
}

/* 轮播图容器样式 */
.carousel-container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* 轮播图图片样式 */
.carousel-image {
  width: 100%;
  height: 400px;
  object-fit: contain;
  cursor: pointer;
  background-color: #f8f9fa;
  transition: transform 0.3s ease;
}

.carousel-image:hover {
  transform: scale(1.02);
}

/* 切换按钮样式 */
.carousel-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #d9d9d9;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: all 0.3s ease;
}

.carousel-nav-btn:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav-btn.prev {
  left: 10px;
}

.carousel-nav-btn.next {
  right: 10px;
}

/* 图片预览Modal样式 */
.image-preview-modal .ant-modal-body {
  padding: 20px;
  text-align: center;
}

.image-preview-modal .ant-image {
  max-width: 100%;
  max-height: 50vh;
}

/* 预览模式轮播图样式 */
.image-preview-modal .ant-carousel .slick-dots {
  bottom: 20px;
}

.image-preview-modal .ant-carousel .slick-dots li button {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.image-preview-modal .ant-carousel .slick-dots li.slick-active button {
  background: #1890ff;
  border-color: #1890ff;
}

/* 预览模式切换按钮样式 */
.preview-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  transition: all 0.3s ease;
}

.preview-nav-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.1);
  color: white;
}

.preview-nav-btn.prev {
  left: 20px;
}

.preview-nav-btn.next {
  right: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .carousel-container {
    max-width: 100%;
    margin: 0;
  }

  .carousel-image {
    height: 300px;
  }

  .carousel-nav-btn {
    width: 35px;
    height: 35px;
  }

  .carousel-nav-btn.prev {
    left: 5px;
  }

  .carousel-nav-btn.next {
    right: 5px;
  }

  /* 预览模式响应式 */
  .preview-nav-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .preview-nav-btn.prev {
    left: 10px;
  }

  .preview-nav-btn.next {
    right: 10px;
  }

  /* 预览Modal在平板上的调整 */
  .image-preview-modal .ant-modal {
    width: 80% !important;
  }
}

@media (max-width: 480px) {
  .carousel-image {
    height: 250px;
  }

  .carousel-nav-btn {
    width: 30px;
    height: 30px;
  }

  /* 预览模式小屏幕适配 */
  .preview-nav-btn {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .preview-nav-btn.prev {
    left: 5px;
  }

  .preview-nav-btn.next {
    right: 5px;
  }

  /* 预览Modal在手机上的调整 */
  .image-preview-modal .ant-modal {
    width: 95% !important;
  }

  .image-preview-modal .ant-image {
    max-height: 40vh;
  }
}
