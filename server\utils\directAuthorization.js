const fs = require('fs');
const { createHash } = require('crypto');
const os = require('os');
const { exec } = require('child_process');
const { createClient } = require('@supabase/supabase-js');

// Supabase配置
const supabaseUrl = 'https://wufltkpqoifjkvnqryyp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind1Zmx0a3Bxb2lmamt2bnFyeXlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMjU1MDksImV4cCI6MjA2ODYwMTUwOX0.0DEAm9xhaZfD1Q0R1XiNh1oKRJjWRg0KFRWV4zMWv88';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 本地存储授权信息的文件路径
const AUTH_FILE_PATH = '/www/wwwroot/ZiZhuTiHuo_System-main/server/.zizhutihuosystem_auth';

/**
 * 生成硬件指纹
 */
async function generateHardwareFingerprint() {
  // 收集硬件信息
  const cpuInfo = os.cpus()[0]?.model || 'unknown';
  const totalMemory = os.totalmem();
  const hostname = os.hostname();
  
  // 对于Windows系统，尝试获取更多硬件信息
  const getWindowsSystemInfo = () => {
    return new Promise((resolve) => {
      if (process.platform !== 'win32') {
        return resolve('');
      }
      
      // 获取主板序列号和BIOS信息
      exec('wmic csproduct get uuid', (error, stdout) => {
        if (error) {
          return resolve('');
        }
        const systemUUID = stdout.toString().trim().split('\n')[1] || '';
        resolve(systemUUID);
      });
    });
  };

  const systemUUID = await getWindowsSystemInfo();
  
  // 组合信息并生成指纹
  const hardwareInfo = `${cpuInfo}|${totalMemory}|${hostname}|${systemUUID}`;
  const fingerprint = createHash('sha256').update(hardwareInfo).digest('hex');
  
  return fingerprint;
}

/**
 * 获取设备详细信息
 */
function getDeviceDetails() {
  return {
    hostname: os.hostname(),
    platform: os.platform(),
    release: os.release(),
    arch: os.arch(),
    cpuModel: os.cpus()[0]?.model || 'unknown',
    totalMemory: os.totalmem(),
    freeMemory: os.freemem(),
    osType: os.type(),
    osVersion: os.version(),
    userInfo: os.userInfo().username
  };
}

/**
 * 检查本地是否已授权
 */
async function isLocallyAuthorized() {
  console.log('检查本地授权状态...');
  console.log('授权文件路径:', AUTH_FILE_PATH);

  try {
    // 检查本地授权文件是否存在
    console.log('检查文件是否存在:', fs.existsSync(AUTH_FILE_PATH));

    if (!fs.existsSync(AUTH_FILE_PATH)) {
      console.log('本地授权文件不存在');
      return false;
    }

    // 检查文件状态
    const stats = fs.statSync(AUTH_FILE_PATH);
    console.log('文件大小:', stats.size, '字节');
    console.log('文件修改时间:', stats.mtime);

    // 读取文件内容
    const fileContent = fs.readFileSync(AUTH_FILE_PATH, 'utf8');
    console.log('文件原始内容:', fileContent);
    console.log('文件内容长度:', fileContent.length);

    if (!fileContent || fileContent.trim().length === 0) {
      console.log('授权文件为空');
      return false;
    }

    // 读取本地授权信息
    const authData = JSON.parse(fileContent);
    console.log('本地授权信息:', authData);
    
    // 生成当前设备的硬件指纹
    const fingerprint = await generateHardwareFingerprint();
    console.log('当前设备的硬件指纹:', fingerprint);
    
    // 验证本地存储的指纹与当前设备指纹是否匹配
    if (authData.hardwareFingerprint !== fingerprint) {
      console.log('设备指纹不匹配，授权无效');
      return false;
    }
    
    // 直接查询授权码和设备绑定状态
    const { data, error } = await supabase.rpc('verify_authorization', {
      hardware_fingerprint: fingerprint
    });
    
    if (error) {
      console.error('验证授权时发生错误:', error);
      return false;
    }
    
    console.log('授权验证结果:', data);
    
    if (!data || !data.valid) {
      console.log('授权验证失败');
      return false;
    }
    
    console.log('授权验证成功');
    return true;
  } catch (error) {
    console.error('授权验证过程中发生错误:', error);
    return false;
  }
}

/**
 * 验证授权码并绑定设备
 */
async function verifyAndBindAuthCode(authCode) {
  console.log('开始验证授权码:', authCode);
  
  try {
    // 生成硬件指纹
    const fingerprint = await generateHardwareFingerprint();
    console.log('设备指纹:', fingerprint);
    
    // 获取设备详细信息
    const deviceDetails = getDeviceDetails();
    
    // 使用存储过程而不是直接查询，绕过RLS策略问题
    const { data, error } = await supabase.rpc('validate_auth_code', {
      p_code: authCode
    });
    
    if (error) {
      console.error('验证授权码存储过程错误:', error);
      return { success: false, message: '授权码验证过程中发生错误，请联系管理员' };
    }
    
    console.log('授权码验证结果:', data);
    
    if (!data || !data.valid || !data.auth_code_id) {
      return { success: false, message: '授权码无效或已被使用' };
    }
    
    // 使用绑定设备存储过程
    const { data: bindResult, error: bindError } = await supabase.rpc('bind_device_to_code', {
      p_code: authCode,
      p_hardware_fingerprint: fingerprint,
      p_hardware_details: deviceDetails,
      p_ip: null
    });
    
    if (bindError) {
      console.error('设备绑定过程错误:', bindError);
      return { success: false, message: '设备绑定失败，请联系管理员' };
    }
    
    console.log('设备绑定结果:', bindResult);
    
    if (!bindResult) {
      return { success: false, message: '设备绑定失败' };
    }
    
    // 保存授权信息到本地
    saveLocalAuthInfo(data.auth_code_id, fingerprint);
    
    return { success: true, message: '授权成功并绑定到当前设备' };
  } catch (error) {
    console.error('授权验证过程中发生错误:', error);
    return { success: false, message: '授权验证过程中发生错误' };
  }
}

/**
 * 保存授权信息到本地文件
 */
function saveLocalAuthInfo(authCodeId, fingerprint) {
  try {
    const authInfo = {
      authCodeId,
      hardwareFingerprint: fingerprint,
      authorizedAt: new Date().toISOString()
    };
    
    // 保存授权信息但不显示路径
    fs.writeFileSync(AUTH_FILE_PATH, JSON.stringify(authInfo), 'utf8');
    console.log('授权信息保存成功');
  } catch (error) {
    console.error('保存授权信息到本地文件失败:', error);
  }
}

module.exports = {
  isLocallyAuthorized,
  verifyAndBindAuthCode,
  generateHardwareFingerprint
}; 