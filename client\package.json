{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-client", "version": "1.0.0", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@reduxjs/toolkit": "^2.0.1", "antd": "^5.12.2", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}