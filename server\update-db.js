const { sequelize } = require('./config/database');

async function updateDatabase() {
  try {
    console.log('开始更新数据库...');

    // 执行原始SQL来修改files表
    await sequelize.query(`
      ALTER TABLE files 
      MODIFY COLUMN file_path VARCHAR(255) NULL COMMENT '文件路径',
      MODIFY COLUMN file_size INT NULL COMMENT '文件大小(KB)',
      ADD COLUMN important_text TEXT NULL COMMENT '网盘链接' AFTER description
    `);

    console.log('数据库更新成功！');
    process.exit(0);
  } catch (error) {
    console.error('数据库更新失败:', error);
    process.exit(1);
  }
}

// 执行更新
updateDatabase(); 