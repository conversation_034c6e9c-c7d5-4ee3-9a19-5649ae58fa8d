import React, { useEffect } from 'react';
import { Card, Form, Input, Button, Alert, Typography, Space, App } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { changeAdminPassword, clearPasswordState } from '../../store/slices/adminSlice';
import AdminLayout from '../../components/layouts/AdminLayout';

const { Title } = Typography;

const ChangePassword = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { message } = App.useApp();
  const { changingPassword, passwordChanged, passwordError } = useSelector((state) => state.admin);
  
  useEffect(() => {
    return () => {
      dispatch(clearPasswordState());
    };
  }, [dispatch]);
  
  useEffect(() => {
    if (passwordChanged) {
      message.success('密码修改成功');
      form.resetFields();
    }
  }, [passwordChanged, form, message]);

  const handleSubmit = (values) => {
    dispatch(changeAdminPassword(values));
  };

  // 确认密码验证
  const validateConfirmPassword = ({ getFieldValue }) => ({
    validator(_, value) {
      if (!value || getFieldValue('newPassword') === value) {
        return Promise.resolve();
      }
      return Promise.reject(new Error('两次输入的密码不一致'));
    },
  });

  return (
    <AdminLayout>
      <Card>
        <Title level={2} style={{ marginBottom: 24 }}>修改密码</Title>
        
        {passwordError && (
          <Alert
            message="修改失败"
            description={passwordError}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="currentPassword"
            label="当前密码"
            rules={[
              { required: true, message: '请输入当前密码' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请输入当前密码" 
            />
          </Form.Item>
          
          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度不能少于6个字符' }
            ]}
            hasFeedback
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请输入新密码" 
            />
          </Form.Item>
          
          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              validateConfirmPassword
            ]}
            hasFeedback
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请再次输入新密码" 
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={changingPassword}
              >
                修改密码
              </Button>
              <Button 
                onClick={() => form.resetFields()}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </AdminLayout>
  );
};

// 使用App.useApp()需要将组件包裹在App中
const ChangePasswordWithApp = () => (
  <App>
    <ChangePassword />
  </App>
);

export default ChangePasswordWithApp; 