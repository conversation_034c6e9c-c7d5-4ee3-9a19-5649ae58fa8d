const { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } = require('../models');
const { generateToken } = require('../utils/jwt');
const crypto = require('crypto');

/**
 * 获取设备指纹
 * @param {Object} req - Express请求对象
 * @returns {String} - 设备指纹
 */
const getDeviceFingerprint = (req) => {
  // 获取IP地址
  const ip = req.ip || req.connection.remoteAddress;
  // 获取用户代理
  const userAgent = req.headers['user-agent'] || '';
  
  // 使用IP和User-Agent生成设备指纹
  const fingerprint = crypto.createHash('md5')
    .update(`${ip}_${userAgent}`)
    .digest('hex');
  
  return fingerprint;
};

/**
 * 验证卡密并生成用户令牌
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const verifyCardKey = async (req, res) => {
  try {
    const { key_code } = req.body;
    
    if (!key_code) {
      return res.status(400).json({ success: false, message: '卡密不能为空' });
    }

    // 查询卡密
    const cardKey = await CardKey.findOne({ where: { key_code } });
    
    if (!cardKey) {
      return res.status(400).json({ success: false, message: '无效的卡密' });
    }

    if (!cardKey.status) {
      return res.status(400).json({ success: false, message: '卡密已失效' });
    }

    // 检查是否过期
    if (cardKey.expire_time && new Date(cardKey.expire_time) < new Date()) {
      return res.status(400).json({ success: false, message: '卡密已过期' });
    }

    // 获取设备指纹
    const deviceFingerprint = getDeviceFingerprint(req);

    // 检查是否为同一设备重复使用卡密
    let isReuse = false;
    
    // 处理单设备类型卡密
    if (cardKey.key_type === 'single') {
      // 只有在卡密已经被使用并且有used_file_id时，才检查是否是同设备
      if (cardKey.is_used && cardKey.used_file_id) {
        // 检查之前用过这个卡密的是不是当前设备
        if (cardKey.used_devices) {
          try {
            const usedDevices = JSON.parse(cardKey.used_devices);
            if (usedDevices.includes(deviceFingerprint)) {
              // 同一设备重复使用，返回特殊状态
              isReuse = true;
            } else {
              return res.status(400).json({ success: false, message: '卡密已被使用' });
            }
          } catch (err) {
            console.error('解析已使用设备列表错误:', err);
            return res.status(400).json({ success: false, message: '卡密已被使用' });
          }
        } else {
          return res.status(400).json({ success: false, message: '卡密已被使用' });
        }
      }
      // 移除首次验证时记录设备和标记卡密已使用的逻辑
    }
    // 处理多设备类型卡密
    else if (cardKey.key_type === 'multi') {
      // 解析已使用设备列表
      let usedDevices = [];
      if (cardKey.used_devices) {
        try {
          usedDevices = JSON.parse(cardKey.used_devices);
          // 确保usedDevices始终是字符串数组，处理可能存在的对象格式
          usedDevices = usedDevices.map(entry => 
            typeof entry === 'object' ? entry.device : entry
          );
        } catch (err) {
          console.error('解析已使用设备列表错误:', err);
          usedDevices = [];
        }
      }

      // 只在卡密有used_file_id并且当前设备在已使用列表中时，才标记为重用
      if (cardKey.used_file_id && usedDevices.includes(deviceFingerprint)) {
        // 同一设备重复使用，返回特殊状态
        isReuse = true;
      } else if (cardKey.is_used && usedDevices.length >= cardKey.max_devices) {
        // 检查是否超出最大设备数
        return res.status(400).json({ success: false, message: '卡密已达到最大使用设备数' });
      }
      // 移除首次验证时记录设备和标记卡密已使用的逻辑
    }

    // 生成用户令牌
    const token = generateToken({
      id: cardKey.id,
      key_code: cardKey.key_code,
      type: 'user',
      device: deviceFingerprint
    }, '2h'); // 令牌有效期2小时

    // 如果是重复使用，返回特殊状态
    if (isReuse) {
      return res.json({
        success: true,
        message: '验证成功',
        isReuse: true,
        data: { token }
      });
    }

    return res.json({ 
      success: true, 
      message: '验证成功', 
      data: { token }
    });
  } catch (error) {
    console.error('卡密验证错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 管理员登录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const adminLogin = async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ success: false, message: '用户名和密码不能为空' });
    }

    // 查询管理员
    const admin = await Admin.findOne({ where: { username } });
    
    if (!admin) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    if (!admin.status) {
      return res.status(403).json({ success: false, message: '账户已被禁用' });
    }

    // 验证密码
    const isPasswordValid = await admin.validPassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    // 更新最后登录时间
    await admin.update({ last_login: new Date() });

    // 生成管理员令牌
    const token = generateToken({
      id: admin.id,
      username: admin.username,
      type: 'admin'
    }, '24h'); // 令牌有效期24小时

    return res.json({ 
      success: true, 
      message: '登录成功', 
      data: { token, admin: { id: admin.id, username: admin.username } }
    });
  } catch (error) {
    console.error('管理员登录错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 修改管理员密码
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const changeAdminPassword = async (req, res) => {
  try {
    const adminId = req.admin.id; // 从JWT令牌获取管理员ID
    const { currentPassword, newPassword, confirmPassword } = req.body;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({ success: false, message: '当前密码、新密码和确认密码都不能为空' });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({ success: false, message: '新密码和确认密码不匹配' });
    }

    // 验证密码复杂度
    if (newPassword.length < 6) {
      return res.status(400).json({ success: false, message: '新密码长度不能少于6个字符' });
    }

    // 查询管理员
    const admin = await Admin.findByPk(adminId);
    
    if (!admin) {
      return res.status(404).json({ success: false, message: '管理员账户不存在' });
    }

    // 验证当前密码
    const isPasswordValid = await admin.validPassword(currentPassword);
    if (!isPasswordValid) {
      return res.status(401).json({ success: false, message: '当前密码错误' });
    }

    // 更新密码
    await admin.update({ password: newPassword });

    return res.json({ success: true, message: '密码修改成功' });
  } catch (error) {
    console.error('修改管理员密码错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 检查令牌有效性和重用状态
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const checkToken = async (req, res) => {
  try {
    // 从用户令牌中获取信息
    const { id, key_code, device } = req.user;
    
    // 查询卡密
    const cardKey = await CardKey.findOne({ where: { id, key_code } });
    
    if (!cardKey) {
      return res.status(401).json({ success: false, message: '无效的卡密' });
    }

    if (!cardKey.status) {
      return res.status(401).json({ success: false, message: '卡密已失效' });
    }

    // 检查是否过期
    if (cardKey.expire_time && new Date(cardKey.expire_time) < new Date()) {
      return res.status(401).json({ success: false, message: '卡密已过期' });
    }

    // 检查是否是重复使用卡密
    let isReuse = false;
    
    // 处理单设备类型卡密
    if (cardKey.key_type === 'single') {
      // 只有在已提取文件的情况下，才标记为重用
      if (cardKey.is_used && cardKey.used_file_id) {
        try {
          const usedDevices = JSON.parse(cardKey.used_devices || '[]');
          if (usedDevices.includes(device)) {
            isReuse = true;
          }
        } catch (err) {
          console.error('解析已使用设备列表错误:', err);
        }
      }
    }
    // 处理多设备类型卡密
    else if (cardKey.key_type === 'multi') {
      // 解析已使用设备列表
      try {
        const usedDevices = JSON.parse(cardKey.used_devices || '[]');
        // 确保usedDevices始终是字符串数组，处理可能存在的对象格式
        const normalizedDevices = usedDevices.map(entry => 
          typeof entry === 'object' ? entry.device : entry
        );
        // 只有在已提取文件的情况下，才标记为重用
        if (cardKey.used_file_id && normalizedDevices.includes(device)) {
          isReuse = true;
        }
      } catch (err) {
        console.error('解析已使用设备列表错误:', err);
      }
    }

    return res.json({
      success: true,
      isReuse: isReuse
    });
  } catch (error) {
    console.error('检查令牌错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

module.exports = {
  verifyCardKey,
  adminLogin,
  changeAdminPassword,
  checkToken
}; 