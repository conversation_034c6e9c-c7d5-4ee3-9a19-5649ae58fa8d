const { Op } = require('sequelize');
const { File, CardKey, OperationLog, Category } = require('../models');
const { sequelize } = require('../config/database');

/**
 * 获取提取记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getExtractionRecords = async (req, res) => {
  try {
    const { page = 1, limit = 10, fileType, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const where = {
      operation_type: '提取文件'
    };
    
    // 如果有日期范围，添加到查询条件
    if (startDate && endDate) {
      where.create_time = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      where.create_time = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.create_time = {
        [Op.lte]: new Date(endDate)
      };
    }

    // 获取提取记录
    const { count, rows } = await OperationLog.findAndCountAll({
      where,
      attributes: ['id', 'operation_type', 'operation_desc', 'operator_ip', 'card_key', 'file_id', 'create_time'],
      include: [
        {
          model: File,
          as: 'file',
          attributes: ['id', 'file_name', 'file_type', 'file_size', 'category_id'],
          include: [
            {
              model: Category,
              attributes: ['id', 'name']
            }
          ],
          required: false
        }
      ],
      order: [['create_time', 'DESC']],
      offset,
      limit: parseInt(limit)
    });
    
    // 如果需要根据文件类型筛选，进行后处理
    let filteredRows = rows;
    let filteredCount = count;
    
    if (fileType && fileType !== 'all') {
      // 假设 'netdisk' 表示网盘类资料，'other' 表示非网盘类资料
      const isNetdisk = fileType === 'netdisk';
      
      filteredRows = rows.filter(log => {
        // 判断是否为网盘类资料（file_size 为 null 或 0 的通常是网盘链接）
        const isFileNetdisk = !log.file || log.file.file_size === null || log.file.file_size === 0;
        return isNetdisk ? isFileNetdisk : !isFileNetdisk;
      });
      
      filteredCount = filteredRows.length;
    }

    return res.json({
      success: true,
      data: {
        total: filteredCount,
        page: parseInt(page),
        limit: parseInt(limit),
        records: filteredRows
      }
    });
  } catch (error) {
    console.error('获取提取记录错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取统计数据（文件提取总数、今日提取数）
 */
const getStatsData = async (req, res) => {
  try {
    // 获取总提取数
    const totalExtractions = await OperationLog.count({
      where: {
        operation_type: '提取文件'
      }
    });
    
    // 获取今日提取数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayExtractions = await OperationLog.count({
      where: {
        operation_type: '提取文件',
        create_time: {
          [Op.gte]: today
        }
      }
    });
    
    // 获取网盘类资料提取数
    const netdiskExtractions = await OperationLog.count({
      where: {
        operation_type: '提取文件'
      },
      include: [
        {
          model: File,
          as: 'file',
          required: true,
          where: {
            [Op.or]: [
              { file_size: null },
              { file_size: 0 }
            ]
          }
        }
      ]
    });
    
    // 获取非网盘类资料提取数
    const otherExtractions = await OperationLog.count({
      where: {
        operation_type: '提取文件'
      },
      include: [
        {
          model: File,
          as: 'file',
          required: true,
          where: {
            file_size: {
              [Op.and]: {
                [Op.ne]: null,
                [Op.gt]: 0
              }
            }
          }
        }
      ]
    });

    // ===== 新增统计数据 =====

    // 卡密统计
    const totalKeys = await CardKey.count();
    const usedKeys = await CardKey.count({ where: { is_used: true } });
    const unusedKeys = await CardKey.count({ where: { is_used: false } });

    // 文件统计
    const totalFiles = await File.count({ where: { status: true } });
    const totalDownloads = await File.sum('download_count', { where: { status: true } }) || 0;

    // 分类统计
    const categoryCount = await Category.count();

    // 近7天提取记录统计
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    // 按天统计近7天提取记录
    const dailyStats = await OperationLog.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('create_time')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        operation_type: '提取文件',
        create_time: {
          [Op.gte]: sevenDaysAgo
        }
      },
      group: [sequelize.fn('DATE', sequelize.col('create_time'))],
      order: [[sequelize.fn('DATE', sequelize.col('create_time')), 'ASC']]
    });

    // 计算近7天提取总量
    const last7DaysExtractions = await OperationLog.count({
      where: {
        operation_type: '提取文件',
        create_time: {
          [Op.gte]: sevenDaysAgo
        }
      }
    });

    return res.json({
      success: true,
      data: {
        // 原有数据
        totalExtractions,
        todayExtractions,
        netdiskExtractions,
        otherExtractions,
        
        // 新增数据 - 仪表盘所需
        keyStats: {
          total: totalKeys,
          used: usedKeys,
          unused: unusedKeys
        },
        fileStats: {
          total: totalFiles,
          totalDownloads
        },
        categoryCount,
        dailyStats,
        last7DaysExtractions
      }
    });
  } catch (error) {
    console.error('获取统计数据错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 删除提取记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteExtractionRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询记录
    const record = await OperationLog.findByPk(id);
    
    if (!record) {
      return res.status(404).json({ success: false, message: '提取记录不存在' });
    }
    
    // 检查是否为提取文件记录
    if (record.operation_type !== '提取文件') {
      return res.status(400).json({ success: false, message: '只能删除提取文件记录' });
    }
    
    // 删除记录
    await record.destroy();
    
    return res.json({
      success: true,
      message: '提取记录删除成功',
      data: { id }
    });
  } catch (error) {
    console.error('删除提取记录错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 批量删除提取记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchDeleteExtractionRecords = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, message: '请选择要删除的记录' });
    }
    
    // 查询所有要删除的记录，确保它们存在并且是提取文件记录
    const records = await OperationLog.findAll({
      where: {
        id: { [Op.in]: ids }
      }
    });
    
    // 检查是否所有记录都存在
    if (records.length !== ids.length) {
      return res.status(404).json({ success: false, message: '部分提取记录不存在' });
    }
    
    // 检查是否所有记录都是提取文件记录
    const nonExtractionRecords = records.filter(record => record.operation_type !== '提取文件');
    if (nonExtractionRecords.length > 0) {
      return res.status(400).json({ success: false, message: '只能删除提取文件记录' });
    }
    
    // 批量删除记录
    const result = await OperationLog.destroy({
      where: {
        id: { [Op.in]: ids },
        operation_type: '提取文件'
      }
    });
    
    return res.json({
      success: true,
      message: `成功删除 ${result} 条提取记录`,
      data: { deleted: result, ids }
    });
  } catch (error) {
    console.error('批量删除提取记录错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取热门文件排行榜
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getPopularFiles = async (req, res) => {
  try {
    const { limit = 10, days = 30 } = req.query;

    // 获取热门文件（按下载次数排序）- 简化查询，不限制时间范围
    const popularFiles = await File.findAll({
      where: {
        status: true
      },
      include: [
        {
          model: Category,
          attributes: ['id', 'name'],
          required: false
        }
      ],
      attributes: [
        'id', 'file_name', 'file_type', 'file_size',
        'download_count', 'upload_time', 'description'
      ],
      order: [['download_count', 'DESC']],
      limit: parseInt(limit)
    });

    // 简化提取统计查询 - 使用Sequelize ORM而不是原生SQL
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const extractionStats = await OperationLog.findAll({
      attributes: [
        'file_id',
        [sequelize.fn('COUNT', sequelize.col('OperationLog.id')), 'extraction_count']
      ],
      where: {
        operation_type: '提取文件',
        create_time: {
          [Op.gte]: startDate
        }
      },
      include: [
        {
          model: File,
          as: 'file',
          attributes: ['id', 'file_name', 'file_type', 'file_size'],
          include: [
            {
              model: Category,
              attributes: ['id', 'name'],
              required: false
            }
          ],
          required: true,
          where: { status: true }
        }
      ],
      group: ['file_id'],
      order: [[sequelize.fn('COUNT', sequelize.col('OperationLog.id')), 'DESC']],
      limit: parseInt(limit)
    });

    // 格式化提取统计数据
    const popularByExtractions = extractionStats;

    return res.json({
      success: true,
      data: {
        popularByDownloads: popularFiles,
        popularByExtractions: popularByExtractions
      }
    });
  } catch (error) {
    console.error('获取热门文件排行榜错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取详细数据分析
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getDetailedAnalytics = async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 计算时间范围
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    startDate.setHours(0, 0, 0, 0);

    // 简化查询 - 按分类统计文件数量
    const categoryStats = await Category.findAll({
      attributes: [
        'id',
        'name',
        [sequelize.literal('(SELECT COUNT(*) FROM files WHERE files.category_id = Category.id AND files.status = 1)'), 'file_count']
      ],
      order: [['name', 'ASC']]
    });

    // 按文件类型统计
    const fileTypeStats = await File.findAll({
      attributes: [
        'file_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        status: true
      },
      group: ['file_type'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']]
    });

    // 按月统计趋势（最近6个月，简化查询）
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyStats = await OperationLog.findAll({
      attributes: [
        [sequelize.fn('DATE_FORMAT', sequelize.col('create_time'), '%Y-%m'), 'month'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'extraction_count']
      ],
      where: {
        operation_type: '提取文件',
        create_time: {
          [Op.gte]: sixMonthsAgo
        }
      },
      group: [sequelize.fn('DATE_FORMAT', sequelize.col('create_time'), '%Y-%m')],
      order: [[sequelize.fn('DATE_FORMAT', sequelize.col('create_time'), '%Y-%m'), 'ASC']]
    });

    // 按小时统计（24小时分布）
    const hourlyStats = await OperationLog.findAll({
      attributes: [
        [sequelize.fn('HOUR', sequelize.col('create_time')), 'hour'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        operation_type: '提取文件',
        create_time: {
          [Op.gte]: startDate
        }
      },
      group: [sequelize.fn('HOUR', sequelize.col('create_time'))],
      order: [[sequelize.fn('HOUR', sequelize.col('create_time')), 'ASC']]
    });

    // 按日统计（最近指定天数的每日提取趋势）
    const dailyStats = await OperationLog.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('create_time')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'extraction_count']
      ],
      where: {
        operation_type: '提取文件',
        create_time: {
          [Op.gte]: startDate
        }
      },
      group: [sequelize.fn('DATE', sequelize.col('create_time'))],
      order: [[sequelize.fn('DATE', sequelize.col('create_time')), 'ASC']]
    });

    return res.json({
      success: true,
      data: {
        categoryStats,
        fileTypeStats,
        monthlyStats,
        hourlyStats,
        dailyStats
      }
    });
  } catch (error) {
    console.error('获取详细数据分析错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

module.exports = {
  getExtractionRecords,
  getStatsData,
  deleteExtractionRecord,
  batchDeleteExtractionRecords,
  getPopularFiles,
  getDetailedAnalytics
};