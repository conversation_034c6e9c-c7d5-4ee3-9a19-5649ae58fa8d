const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileController');
const { verifyUserToken } = require('../middlewares/auth');
const { logOperation } = require('../middlewares/logger');

// 获取文件列表
router.get(
  '/',
  verifyUserToken,
  fileController.getFiles
);

// 获取文件详情
router.get(
  '/:id',
  verifyUserToken,
  fileController.getFileDetails
);

// 提取文件
router.post(
  '/extract/:fileId',
  verifyUserToken,
  logOperation('提取文件', '用户提取文件资料'),
  fileController.extractFile
);

// 添加直接下载文件的路由（用于管理员预览）
router.get(
  '/:id/download',
  fileController.directDownload
);

// 简介图片访问路由（用户端）
router.get(
  '/preview/:filename',
  verifyUserToken,
  fileController.getPreviewImage
);

// 下载文件 - 移至路由末尾，避免与/:id冲突
router.get(
  '/download/:fileId',
  fileController.downloadFile
);

module.exports = router;