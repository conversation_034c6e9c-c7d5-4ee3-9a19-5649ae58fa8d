import React, { useState, useEffect, useRef } from 'react';
import { Card, Input, Button, Typography, Layout, Space, message, notification } from 'antd';
import { KeyOutlined, ExclamationCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { verifyCard, clearError, clearReuse, logout } from '../store/slices/authSlice';

const { Title, Paragraph } = Typography;
const { Header, Content, Footer } = Layout;

const Home = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuthenticated, loading, error, isReuse } = useSelector((state) => state.auth);
  const [cardKeyInput, setCardKeyInput] = useState('');
  const notificationKeyRef = useRef(null);
  
  // 如果已登录且不是重复使用卡密，重定向到文件列表
  useEffect(() => {
    if (isAuthenticated && !isReuse) {
      navigate('/files');
    } else if (isAuthenticated && isReuse) {
      // 如果是重复使用卡密，不允许跳转，直接登出
      dispatch(logout());
    }
  }, [isAuthenticated, isReuse, navigate, dispatch]);
  
  // 添加全局点击事件监听器
  useEffect(() => {
    const handleGlobalClick = () => {
      // 关闭所有通知
      notification.destroy();
      
      // 清除错误和重用状态
      if (error) {
        dispatch(clearError());
      }
      if (isReuse) {
        dispatch(clearReuse());
      }
    };
    
    // 添加点击事件监听器
    document.addEventListener('click', handleGlobalClick);
    
    // 清理函数
    return () => {
      document.removeEventListener('click', handleGlobalClick);
    };
  }, [error, isReuse, dispatch]);
  
  // 处理错误和重用状态的通知
  useEffect(() => {
    if (error) {
      // 使用key确保通知唯一
      const key = `error-${Date.now()}`;
      notificationKeyRef.current = key;
      
      notification.error({
        message: '验证失败',
        description: error,
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
        duration: 1.5, // 自动消失时间，单位秒
        placement: 'top',
        key,
        onClose: () => {
          dispatch(clearError());
        }
      });
    }
    
    if (isReuse) {
      // 使用key确保通知唯一
      const key = `reuse-${Date.now()}`;
      notificationKeyRef.current = key;
      
      notification.warning({
        message: '重复使用卡密提示',
        description: '该设备已使用过此卡密，无法再次提取文件。',
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
        duration: 1.5, // 自动消失时间，单位秒
        placement: 'top',
        key,
        onClose: () => {
          dispatch(clearReuse());
        }
      });
    }
  }, [error, isReuse, dispatch]);

  // 处理卡密验证
  const handleVerifyCard = async (value, e) => {
    // 如果有事件对象，阻止事件冒泡
    if (e) {
      e.stopPropagation();
    }
    
    // 使用传入的value或者从state获取卡密值
    const keyToVerify = value || cardKeyInput;
    
    if (!keyToVerify || keyToVerify.trim() === '') {
      message.error('请输入卡密');
      return;
    }

    const trimmedKey = keyToVerify.trim();
    
    try {
      // 直接在首页验证卡密
      const result = await dispatch(verifyCard(trimmedKey)).unwrap();
      
      // 如果是重复使用的卡密，不跳转，直接清除登录状态
      if (result.isReuse) {
        dispatch(logout());
      }
      // 验证成功后会自动跳转到文件列表页面（通过useEffect监听isAuthenticated）
    } catch (error) {
      // 错误会通过redux state显示
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        position: 'fixed',
        zIndex: 1000,
        width: '100%',
        top: 0
      }}>
        <Title level={4} style={{ color: '#fff', margin: 0, paddingLeft: '5px',fontSize:30 }}>
          自助提取系统
        </Title>
        <Space>
          <Button type="primary" ghost onClick={(e) => {
            e.stopPropagation(); // 阻止事件冒泡
            navigate('/admin/login');
          }}>
            管理员登录
          </Button>
        </Space>
      </Header>

      <Content style={{ 
        padding: '50px 50px', 
        backgroundSize: 'cover', 
        backgroundPosition: 'center',
        marginTop: 64 // 为固定导航栏腾出空间
      }}>
        <div style={{ maxWidth: 1200, margin: '0 auto', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <div style={{ textAlign: 'center', marginBottom: 60 }}>
            <Title style={{ fontSize: 48 }}>自助提取系统</Title>
            <Paragraph style={{ fontSize: 18 }}>
              安全便捷的文件资料提取平台
            </Paragraph>
          </div>

          <div style={{ display: 'flex', justifyContent: 'center', width: '100%' }} onClick={(e) => e.stopPropagation()}>
            <Card
              variant="borderless"
              style={{ width: 400, boxShadow: '0 4px 12px rgba(0,0,0,0.1)', borderRadius: 8 }}
            >
              <Title level={4} style={{ textAlign: 'center', marginBottom: 24 }}>
                输入卡密提取资料
              </Title>
              
              <Input.Search
                placeholder="请输入卡密"
                size="large"
                prefix={<KeyOutlined />}
                enterButton="立即提取"
                value={cardKeyInput}
                onChange={(e) => setCardKeyInput(e.target.value)}
                onSearch={(value, e) => handleVerifyCard(value, e)}
                loading={loading}
                onClick={(e) => e.stopPropagation()}
              />

              <div style={{ marginTop: 24 }}>
                <Paragraph style={{ textAlign: 'center', color: '#666', marginBottom: 0 }}>
                  输入卡密后，您可以查看并提取系统中的文件资料。
                </Paragraph>
              </div>
            </Card>
          </div>

          <div style={{ marginTop: 80, display: 'flex', justifyContent: 'center', gap: 30 }}>
            <Card style={{ width: 300, borderRadius: 8 }} variant="borderless" onClick={(e) => e.stopPropagation()}>
              <div className="text-center" style={{ height: 80, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <KeyOutlined style={{ fontSize: 40, color: '#1890ff' }} />
              </div>
              <Title level={4} className="text-center">一卡一提</Title>
              <Paragraph className="text-center">
                每个卡密仅可提取一个文件资料，保证资源安全可控
              </Paragraph>
            </Card>
            
            <Card style={{ width: 300, borderRadius: 8 }} variant="borderless" onClick={(e) => e.stopPropagation()}>
              <div className="text-center" style={{ height: 80, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <svg width="40" height="40" viewBox="0 0 24 24" fill="#1890ff">
                  <path d="M18 10h-4V4h4v6zm-6 0H8V4h4v6zM6 20h12v-8H6v8zm-2 2V2h16v20H4z" />
                </svg>
              </div>
              <Title level={4} className="text-center">资料丰富</Title>
              <Paragraph className="text-center">
                系统提供多种类型的优质资料，满足不同需求
              </Paragraph>
            </Card>
            
            <Card style={{ width: 300, borderRadius: 8 }} variant="borderless" onClick={(e) => e.stopPropagation()}>
              <div className="text-center" style={{ height: 80, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <svg width="40" height="40" viewBox="0 0 24 24" fill="#1890ff">
                  <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z" />
                </svg>
              </div>
              <Title level={4} className="text-center">安全可靠</Title>
              <Paragraph className="text-center">
                采用多重安全保障机制，保护您的账户和资料安全
              </Paragraph>
            </Card>
          </div>
        </div>
      </Content>

      <Footer style={{ textAlign: 'center' }}>
        自助提取系统 ©{new Date().getFullYear()} 版权所有
      </Footer>
    </Layout>
  );
};

export default Home; 