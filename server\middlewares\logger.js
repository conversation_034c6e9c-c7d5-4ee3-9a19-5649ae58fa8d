const { OperationLog } = require('../models');

/**
 * 记录操作日志到数据库
 * @param {String} type - 操作类型
 * @param {String} description - 操作描述
 * @returns {Function} - Express中间件函数
 */
const logOperation = (type, description) => {
  return async (req, res, next) => {
    // 保存原始的res.json方法
    const originalJson = res.json;

    // 重写res.json方法
    res.json = function(body) {
      // 还原原始方法
      res.json = originalJson;

      // 如果操作成功，记录日志
      if (body && body.success) {
        const logData = {
          operation_type: type,
          operation_desc: description,
          operator_ip: req.ip || req.connection.remoteAddress,
          // 优先从req.extractedCardKey获取卡密信息，这是文件提取时专门添加的
          // 其次从响应体中获取卡密信息，最后从请求参数中获取
          card_key: req.extractedCardKey || 
                    (body.data && body.data.cardKey) || 
                    req.body.key_code || 
                    req.params.keyCode || 
                    null,
          file_id: req.params.fileId || req.body.fileId || null
        };

        // 异步记录日志，不影响响应
        OperationLog.create(logData)
          .catch(err => console.error('记录操作日志失败:', err));
      }

      // 调用原始的json方法发送响应
      return res.json(body);
    };

    next();
  };
};

module.exports = {
  logOperation
}; 