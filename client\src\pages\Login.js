import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Form, Alert, Typography, Layout, Spin, notification } from 'antd';
import { KeyOutlined, ArrowLeftOutlined, WarningOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { verifyCard, clearError, clearReuse, logout } from '../store/slices/authSlice';

const { Title, Paragraph } = Typography;
const { Header, Content, Footer } = Layout;

const Login = () => {
  const [cardKey, setCardKey] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { loading, error, isAuthenticated, isReuse } = useSelector((state) => state.auth);

  // 如果已登录且不是重复使用卡密，重定向到文件列表
  useEffect(() => {
    if (isAuthenticated && !isReuse) {
      navigate('/files');
    } else if (isAuthenticated && isReuse) {
      // 如果是重复使用卡密，显示提示并登出
      notification.warning({
        message: '重复使用卡密提示',
        description: '该设备已使用过此卡密，无法再次提取文件。',
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
        duration: 3
      });
      dispatch(logout());
    }
    
    // 清除之前的错误
    return () => {
      dispatch(clearError());
      if (isReuse) {
        dispatch(clearReuse());
      }
    };
  }, [isAuthenticated, isReuse, navigate, dispatch]);

  const handleSubmit = async () => {
    if (!cardKey) return;
    
    try {
      const result = await dispatch(verifyCard(cardKey)).unwrap();
      // 如果是重复使用的卡密，会在useEffect中处理
    } catch (error) {
      // 错误已在redux中处理
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        display: 'flex', 
        alignItems: 'center',
        position: 'fixed',
        zIndex: 1000,
        width: '100%',
        top: 0
      }}>
        <Title level={4} style={{ color: '#fff', margin: 0 }}>
          自助提取系统
        </Title>
      </Header>

      <Content style={{ 
        padding: '50px 50px', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        marginTop: 64 // 为固定导航栏腾出空间
      }}>
        <Card
          variant="borderless"
          style={{ width: 400, boxShadow: '0 4px 12px rgba(0,0,0,0.1)', borderRadius: 8 }}
        >
          <Link to="/" style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
            <ArrowLeftOutlined style={{ marginRight: 8 }} />
            返回首页
          </Link>

          <Title level={3} style={{ textAlign: 'center', marginBottom: 24 }}>
            输入卡密验证
          </Title>

          {error && (
            <Alert
              message={error}
              type="error"
              style={{ marginBottom: 24 }}
              showIcon
              closable
              onClose={() => dispatch(clearError())}
            />
          )}

          <Form form={form} onFinish={handleSubmit}>
            <Form.Item
              name="cardKey"
              rules={[{ required: true, message: '请输入卡密' }]}
            >
              <Input
                prefix={<KeyOutlined />}
                placeholder="请输入卡密"
                size="large"
                value={cardKey}
                onChange={(e) => setCardKey(e.target.value)}
                disabled={loading}
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                block
                size="large"
                loading={loading}
              >
                验证卡密
              </Button>
            </Form.Item>
          </Form>

          <Paragraph style={{ textAlign: 'center', marginTop: 16, color: '#666' }}>
            输入有效的卡密后，您可以查看并提取系统中的文件资料
          </Paragraph>
        </Card>
      </Content>

      <Footer style={{ textAlign: 'center' }}>
        自助提取系统 ©{new Date().getFullYear()} 版权所有
      </Footer>
    </Layout>
  );
};

export default Login; 