const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CardKey = sequelize.define('CardKey', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  key_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '卡密码'
  },
  is_used: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已使用'
  },
  used_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '使用时间'
  },
  create_time: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  expire_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '过期时间'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态(1:有效,0:无效)'
  },
  used_file_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '已提取的文件ID'
  },
  key_type: {
    type: DataTypes.ENUM('single', 'multi'),
    defaultValue: 'single',
    comment: '卡密类型(single:单设备,multi:多设备)'
  },
  used_devices: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '已使用设备列表,JSON格式存储'
  },
  max_devices: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '最大可用设备数'
  }
}, {
  tableName: 'card_keys',
  timestamps: false
});

module.exports = CardKey; 