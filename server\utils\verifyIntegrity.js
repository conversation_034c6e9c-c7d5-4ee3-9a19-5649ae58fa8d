/**
 * 文件完整性验证脚本
 * 用于在系统启动前验证关键文件是否被修改
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 项目根目录
const basePath = path.resolve(__dirname, '../..');

// 哈希值文件路径
const hashesPath = path.join(__dirname, 'hashValues.json');

/**
 * 计算文件的哈希值
 * @param {String} filePath - 文件路径
 * @returns {String} - 文件哈希值
 */
function calculateFileHash(filePath) {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    const hashSum = crypto.createHash('sha256');
    hashSum.update(fileBuffer);
    return hashSum.digest('hex');
  } catch (error) {
    console.error(`无法计算文件哈希值: ${filePath}`, error);
    return null;
  }
}

/**
 * 验证文件完整性
 */
async function verifyFileIntegrity() {
  console.log('开始验证文件完整性...');
  
  try {
    // 检查哈希值文件是否存在
    if (!fs.existsSync(hashesPath)) {
      console.error('错误: 哈希值文件不存在!');
      console.error('请先运行 initialize-hashes.bat 初始化哈希值');
      process.exit(1);
    }
    
    // 读取存储的哈希值
    const storedHashes = JSON.parse(fs.readFileSync(hashesPath, 'utf8'));
    
    // 验证每个文件的完整性
    const modifiedFiles = [];
    const missingFiles = [];
    
    for (const [filePath, storedHash] of Object.entries(storedHashes)) {
      const absolutePath = path.join(basePath, filePath);
      
      // 检查文件是否存在
      if (!fs.existsSync(absolutePath)) {
        missingFiles.push(filePath);
        continue;
      }
      
      // 计算当前文件的哈希值
      const currentHash = calculateFileHash(absolutePath);
      
      // 比较哈希值
      if (currentHash !== storedHash) {
        modifiedFiles.push(filePath);
      }
    }
    
    // 输出验证结果
    if (missingFiles.length > 0) {
      console.error('\n错误: 以下文件缺失:');
      missingFiles.forEach(file => console.error(` - ${file}`));
      process.exit(1);
    }
    
    if (modifiedFiles.length > 0) {
      console.error('\n错误: 以下文件被修改:');
      modifiedFiles.forEach(file => console.error(` - ${file}`));
      process.exit(1);
    }
    
    console.log('文件完整性验证成功！所有文件均未被修改。');
    process.exit(0);
  } catch (error) {
    console.error('验证文件完整性时发生错误:', error);
    process.exit(1);
  }
}

// 执行验证
verifyFileIntegrity(); 