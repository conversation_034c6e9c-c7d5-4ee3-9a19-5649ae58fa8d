# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a self-service file extraction system (自助提取系统) - a web-based application that allows users to obtain file resources using valid card keys. The system implements a one-card-one-file model where each card key can only extract one file resource.

## Common Development Commands

### Installation and Setup
```bash
# Install all dependencies
npm run install:all

# Or install separately:
cd server && npm install
cd client && npm install
```

### Database Setup
```bash
# Import database schema
mysql -u root -p zizhutihuoxitong < database/schema.sql

# Initialize file integrity hashes (Windows)
initialize-hashes.bat
```

### Development Commands
```bash
# Start backend server (from server directory)
cd server && npm start

# Start frontend development server (from client directory) 
cd client && npm start

# Build frontend for production
cd client && npm run build
```

### System Startup
```bash
# One-click system startup (Windows)
start-system.bat

# Frontend-only startup
start-frontend.bat
```

### Testing and Validation
```bash
# Test database connection
cd server && node test-db.js

# Verify file integrity
cd server && node utils/verifyIntegrity.js
```

## Architecture Overview

### Technology Stack
- **Frontend**: React 18.2.0 + Ant Design + Redux Toolkit + React Router
- **Backend**: Node.js + Express + Sequelize ORM
- **Database**: MySQL 8.0
- **Authentication**: JWT + bcryptjs
- **Authorization**: Supabase-based license system with device fingerprinting
- **File Security**: Hash-based integrity validation

### Core System Components

#### Backend Architecture (`server/`)
- **Models**: Sequelize models for database entities (Admin, CardKey, Category, File, OperationLog)
- **Controllers**: Business logic handlers for API endpoints
- **Routes**: API route definitions and middleware
- **Middleware**: Authentication, logging, and security layers
- **Utils**: Authorization system, file integrity validation, scheduled tasks

#### Frontend Architecture (`client/`)
- **Pages**: Main application views (user and admin interfaces)
- **Components**: Reusable UI components and layouts
- **Store**: Redux state management with slices for different domains
- **Services**: API communication layer

#### Security Features
- **File Integrity**: Hash-based validation prevents code tampering
- **Device Binding**: Hardware fingerprinting prevents license sharing
- **Authorization System**: Supabase-integrated license verification
- **Multi-device Support**: Configurable card key types (single/multi-device)

### Database Schema
- **admins**: Admin user accounts with encrypted passwords
- **card_keys**: Card keys with usage tracking and device binding
- **categories**: File categorization system
- **files**: File metadata with path, type, and preview image support
- **operation_logs**: Comprehensive audit trail

### Key System Flows

#### User File Extraction Flow
1. User enters card key for verification
2. System validates key and checks device restrictions
3. If valid, display available files
4. User selects file and confirms extraction
5. System marks key as used and logs operation
6. User gains access to download/view file

#### Admin Management Flow
1. Admin authentication via JWT
2. Access to card key generation and management
3. File upload with preview image support
4. System analytics and operation logs
5. Category and user management

## Development Guidelines

### Environment Configuration
- Backend requires `.env` file with database credentials and JWT secret
- Frontend proxies API requests to `http://localhost:5000`
- Default ports: Frontend (3000), Backend (5000)

### File Upload Handling
- Files stored in `server/uploads/` with organized subdirectories
- Preview images supported for file introductions
- Both actual files and text-only resources supported

### Authorization System Integration
- System requires valid license on first startup
- License binds to device hardware fingerprint
- File integrity validation runs on every startup
- Failed validation prevents system startup

### State Management
- Frontend uses Redux Toolkit for state management
- Separate slices for auth, admin, categories, and files
- Persistent authentication via localStorage tokens

## Project Structure Notes

### Important Directories
- `XianYu/`: Standalone module for Xianyu product information extraction
- `server/utils/`: Critical security and utility functions
- `client/src/pages/`: User and admin interface components
- `database/`: SQL schema and initialization scripts

### Batch Files (Windows)
- `start-system.bat`: Complete system startup with integrity checks
- `start-frontend.bat`: Frontend-only startup
- `initialize-hashes.bat`: Generate file integrity hashes

### Documentation Files
- `README.md`: Comprehensive project documentation in Chinese
- `开发文档.md`: Detailed development documentation
- `数据库初始化步骤.md`: Database setup instructions
- `文件完整性验证说明.txt`: File integrity validation explanation

## Special Considerations

### Security Requirements
- Never bypass file integrity validation
- Authorization system must remain intact
- Device fingerprinting is critical for license compliance
- All sensitive operations require proper authentication

### Multi-language Support
- Primary language: Chinese (Simplified)
- UI text and documentation in Chinese
- Code comments mix Chinese and English

### Production Deployment
- Frontend builds to static files served by Express
- Requires MySQL database setup and configuration
- File integrity hashes must be initialized before deployment
- License activation required on first run