import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Space,
  Button,
  DatePicker,
  Select,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Divider,
  message,
  App,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  ReloadOutlined,
  FileTextOutlined,
  CloudOutlined,
  FilterOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';
import { useSelector, useDispatch } from 'react-redux';
import { getExtractionRecords, deleteExtractionRecord, batchDeleteExtractionRecords } from '../../store/slices/adminSlice';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 格式化时间
const formatDate = (dateString) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const ExtractionRecords = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState(null);
  const [fileType, setFileType] = useState('all'); // 'all', 'netdisk', 'other'
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [stats, setStats] = useState({
    totalExtractions: 0,
    todayExtractions: 0,
    netdiskExtractions: 0,
    otherExtractions: 0
  });

  const { extractionRecords, totalExtractionRecords, extractionRecordPage, extractionRecordLimit, loadingExtractionRecords } = useSelector(
    (state) => state.admin
  );
  const { admin } = useSelector((state) => state.admin);
  const { message: messageApi, modal } = App.useApp();

  // 计算分页数据
  const pagination = {
    current: extractionRecordPage || 1,
    pageSize: extractionRecordLimit || 10,
    total: totalExtractionRecords || 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条记录`
  };

  // 获取提取记录数据
  const fetchRecords = () => {
    // 构建查询参数
    let params = {
      page: pagination.current,
      limit: pagination.pageSize,
      fileType
    };
    
    // 如果有日期范围，添加到查询参数
    if (dateRange && dateRange.length === 2) {
      params.startDate = dateRange[0].format('YYYY-MM-DD');
      params.endDate = dateRange[1].format('YYYY-MM-DD 23:59:59');
    }

    dispatch(getExtractionRecords(params));
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      if (!adminToken) return;

      const response = await axios.get('/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchRecords();
    fetchStats();
  }, [extractionRecordPage, extractionRecordLimit, fileType, dateRange]);

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    dispatch(getExtractionRecords({
      page: pagination.current,
      limit: pagination.pageSize,
      fileType
    }));
  };

  // 重置筛选
  const handleResetFilter = () => {
    setFileType('all');
    setDateRange(null);
    setSelectedRowKeys([]);
    dispatch(getExtractionRecords({ page: 1 }));
  };

  // 处理删除单条记录
  const handleDelete = (id) => {
    dispatch(deleteExtractionRecord(id))
      .unwrap()
      .then(() => {
        messageApi.success('删除成功');
        fetchStats(); // 刷新统计数据
      })
      .catch((error) => {
        messageApi.error(error || '删除失败');
      });
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      messageApi.warning('请选择要删除的记录');
      return;
    }

    modal.confirm({
      title: '确定要删除选中的记录吗？',
      icon: <ExclamationCircleOutlined />,
      content: `将删除 ${selectedRowKeys.length} 条提取记录，此操作不可恢复。`,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        dispatch(batchDeleteExtractionRecords(selectedRowKeys))
          .unwrap()
          .then((result) => {
            messageApi.success(`成功删除 ${result.deleted} 条记录`);
            setSelectedRowKeys([]);
            fetchStats(); // 刷新统计数据
          })
          .catch((error) => {
            messageApi.error(error || '批量删除失败');
          });
      }
    });
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 60
    },
    {
      title: '提取时间',
      dataIndex: 'create_time',
      render: (text) => formatDate(text),
      width: 180
    },
    {
      title: '资料类型',
      dataIndex: 'file',
      render: (file) => {
        if (!file) return <Tag color="default">未知</Tag>;
        const isNetdisk = !file.file_size || file.file_size === 0;
        return isNetdisk ? 
          <Tag color="blue" icon={<CloudOutlined />}>网盘资料</Tag> : 
          <Tag color="green" icon={<FileTextOutlined />}>文件资料</Tag>;
      },
      width: 100
    },
    {
      title: '资料名称',
      dataIndex: 'file',
      ellipsis: { showTitle: false },
      render: (file) => (
        <Tooltip placement="topLeft" title={file ? file.file_name : '已删除资料'}>
          <span>{file ? file.file_name : '已删除资料'}</span>
        </Tooltip>
      )
    },
    {
      title: '资料分类',
      dataIndex: 'file',
      render: (file) => file && file.Category ? file.Category.name : '-',
      width: 120
    },
    {
      title: '卡密',
      dataIndex: 'card_key',
      width: 140
    },
    {
      title: '操作IP',
      dataIndex: 'operator_ip',
      width: 120
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Popconfirm
          title="确定要删除这条记录吗？"
          onConfirm={() => handleDelete(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            size="small"
          />
        </Popconfirm>
      )
    }
  ];

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys) => setSelectedRowKeys(keys)
  };

  return (
    <div>
      <Title level={3}>提取记录</Title>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总提取次数"
              value={stats.totalExtractions}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日提取次数"
              value={stats.todayExtractions}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="网盘资料提取"
              value={stats.netdiskExtractions}
              prefix={<CloudOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="非网盘资料提取"
              value={stats.otherExtractions}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Space>
              <span>资料类型:</span>
              <Select
                value={fileType}
                onChange={setFileType}
                style={{ width: 150 }}
              >
                <Option value="all">全部</Option>
                <Option value="netdisk">网盘资料</Option>
                <Option value="other">非网盘资料</Option>
              </Select>
            </Space>
          </Col>
          <Col span={10}>
            <Space>
              <span>日期范围:</span>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
              />
            </Space>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={handleResetFilter}
              >
                重置筛选
              </Button>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={fetchRecords}
              >
                刷新数据
              </Button>
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchDelete}
                disabled={selectedRowKeys.length === 0}
              >
                批量删除
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 提取记录表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={extractionRecords}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={pagination}
          loading={loadingExtractionRecords}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};

export default ExtractionRecords; 