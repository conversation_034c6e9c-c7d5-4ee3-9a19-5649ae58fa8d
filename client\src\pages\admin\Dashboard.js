import React, { useEffect, useMemo } from 'react';
import { Row, Col, Card, Statistic, Spin, Typography, Table, Button, Tag, Tooltip } from 'antd';
import { 
  FileOutlined, 
  KeyOutlined, 
  UserOutlined, 
  DownloadOutlined,
  AppstoreOutlined,
  EyeOutlined,
  CloudOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getSystemStats, getCardKeys, getAdminFiles, getExtractionRecords } from '../../store/slices/adminSlice';

const { Title, Text } = Typography;

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const Dashboard = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { stats, loadingStats } = useSelector((state) => state.admin);
  const { files, loadingFiles } = useSelector((state) => state.admin);
  const { cardKeys, loadingKeys } = useSelector((state) => state.admin);
  const { extractionRecords, loadingExtractionRecords } = useSelector((state) => state.admin);

  useEffect(() => {
    dispatch(getSystemStats());
    dispatch(getCardKeys({ page: 1, limit: 5 }));
    dispatch(getAdminFiles({ page: 1, limit: 5 }));
    dispatch(getExtractionRecords({ page: 1, limit: 5 }));
  }, [dispatch]);

  // 计算近7日提取量和登录量
  const weeklyStats = useMemo(() => {
    if (!stats || !stats.dailyStats) return { extractions: 0, logins: 0 };
    
    // 计算总提取量
    const extractions = stats.dailyStats.reduce((sum, item) => sum + parseInt(item.count || 0), 0);
    
    // TODO: 如果有登录量数据，可以在这里计算
    const logins = 0;
    
    return { extractions, logins };
  }, [stats]);

  // 卡密表格列定义
  const keyColumns = [
    {
      title: '卡密',
      dataIndex: 'key_code',
      key: 'key_code',
      render: (text) => <Text copyable>{text}</Text>
    },
    {
      title: '类型',
      dataIndex: 'key_type',
      key: 'key_type',
      width: 80,
      render: (type) => (
        type === 'single' ? '单设备' : type === 'multi' ? '多设备' : type
      )
    },
    {
      title: '设备使用情况',
      dataIndex: 'used_devices',
      key: 'used_devices',
      width: 120,
      render: (used_devices, record) => {
        if (!used_devices) return '-';
        try {
          const devices = JSON.parse(used_devices);
          return `${devices.length || 0}/${record.max_devices || 1}`;
        } catch (e) {
          return '-';
        }
      }
    },
    {
      title: '启用状态',
      dataIndex: 'status',
      key: 'status',
      width: 90,
      render: (status) => (
        <span style={{ 
          color: status ? '#52c41a' : '#f5222d',
          fontWeight: 'bold'
        }}>
          {status ? '启用' : '禁用'}
        </span>
      )
    },
    {
      title: '使用状态',
      dataIndex: 'is_used',
      key: 'is_used',
      width: 90,
      render: (isUsed) => (
        <span style={{ 
          color: isUsed ? '#f5222d' : '#52c41a',
          fontWeight: 'bold'
        }}>
          {isUsed ? '已使用' : '未使用'}
        </span>
      )
    },
    {
      title: '已提取文件',
      dataIndex: 'usedFile',
      key: 'used_file',
      width: 200,
      ellipsis: { showTitle: false },
      render: (usedFile, record) => {
        if (!usedFile && !record.used_file_id) return '-';
        return (
          <Tooltip placement="topLeft" title={usedFile ? usedFile.file_name : '未知文件'}>
            <span>{usedFile ? usedFile.file_name : `ID: ${record.used_file_id}`}</span>
          </Tooltip>
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 150,
      render: (text) => formatDate(text)
    },
    {
      title: '使用时间',
      dataIndex: 'used_time',
      key: 'used_time',
      width: 150,
      render: (text) => text ? formatDate(text) : '-'
    },
    {
      title: '过期时间',
      dataIndex: 'expire_time',
      key: 'expire_time',
      width: 150,
      render: (text) => text ? formatDate(text) : '永不过期'
    }
  ];

  // 文件表格列定义
  const fileColumns = [
    {
      title: '文件名称',
      dataIndex: 'file_name',
      key: 'file_name',
      ellipsis: true
    },
    {
      title: '大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100,
      render: (size) => {
        if (!size) return '-';
        return size < 1024 ? `${size} KB` : `${(size / 1024).toFixed(2)} MB`;
      }
    },
    {
      title: '类型',
      dataIndex: 'file_type',
      key: 'file_type',
      width: 100
    },
    {
      title: '分类',
      dataIndex: ['Category', 'name'],
      key: 'category',
      width: 120,
      render: (text) => text || '未分类'
    },
    {
      title: '下载次数',
      dataIndex: 'download_count',
      key: 'download_count',
      width: 100
    },
    {
      title: '上传时间',
      dataIndex: 'upload_time',
      key: 'upload_time',
      width: 180,
      render: (text) => formatDate(text)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <span style={{ 
          color: status ? '#52c41a' : '#f5222d',
          fontWeight: 'bold'
        }}>
          {status ? '可用' : '禁用'}
        </span>
      )
    }
  ];

  // 提取记录表格列定义
  const extractionColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60
    },
    {
      title: '提取时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (text) => formatDate(text),
      width: 180
    },
    {
      title: '资料类型',
      dataIndex: 'file',
      key: 'file_type',
      render: (file) => {
        if (!file) return <Tag color="default">未知</Tag>;
        const isNetdisk = !file.file_size || file.file_size === 0;
        return isNetdisk ? 
          <Tag color="blue" icon={<CloudOutlined />}>网盘资料</Tag> : 
          <Tag color="green" icon={<FileTextOutlined />}>文件资料</Tag>;
      },
      width: 100
    },
    {
      title: '资料名称',
      dataIndex: 'file',
      key: 'file_name',
      ellipsis: { showTitle: false },
      render: (file) => (
        <Tooltip placement="topLeft" title={file ? file.file_name : '已删除资料'}>
          <span>{file ? file.file_name : '已删除资料'}</span>
        </Tooltip>
      )
    },
    {
      title: '资料分类',
      dataIndex: 'file',
      key: 'category',
      width: 120,
      render: (file) => file && file.Category ? file.Category.name : '-'
    },
    {
      title: '卡密',
      dataIndex: 'card_key',
      key: 'card_key',
      width: 140,
      render: (text) => text || '-'
    },
    {
      title: '操作IP',
      dataIndex: 'operator_ip',
      key: 'operator_ip',
      width: 120
    }
  ];

  return (
    <div>
      <Title level={2}>管理控制台</Title>
      <Text type="secondary" style={{ marginBottom: 24, display: 'block' }}>
        欢迎使用自助提取系统管理后台，以下是系统概览数据。
      </Text>

      {/* 统计卡片 */}
      <Spin spinning={loadingStats}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="卡密总数"
                value={stats?.keyStats?.total || 0}
                prefix={<KeyOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
              <div style={{ color: '#8c8c8c', fontSize: 13, marginTop: 8 }}>
                未使用: {stats?.keyStats?.unused || 0} | 已使用: {stats?.keyStats?.used || 0}
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="文件总数"
                value={stats?.fileStats?.total || 0}
                prefix={<FileOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
              <div style={{ color: '#8c8c8c', fontSize: 13, marginTop: 8 }}>
                &nbsp;
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="分类总数"
                value={stats?.categoryCount || 0}
                prefix={<AppstoreOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
              <div style={{ color: '#8c8c8c', fontSize: 13, marginTop: 8 }}>
                &nbsp;
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="近7日提取量"
                value={weeklyStats.extractions}
                prefix={<DownloadOutlined style={{ color: '#fa8c16' }} />}
                valueStyle={{ color: '#fa8c16' }}
              />
              <div style={{ color: '#8c8c8c', fontSize: 13, marginTop: 8 }}>
                总提取量: {stats?.fileStats?.totalDownloads || 0}
              </div>
            </Card>
          </Col>
        </Row>
      </Spin>

      <div style={{ height: 24 }} /> {/* 间距 */}

      {/* 最近卡密 */}
      <Card
        title="最近卡密"
        extra={<Button type="link" onClick={() => navigate('/admin/card-keys')}>查看全部</Button>}
        style={{ marginBottom: 16 }}
      >
        <Spin spinning={loadingKeys}>
          <Table
            columns={keyColumns}
            dataSource={cardKeys.map(key => ({ ...key, key: key.id }))}
            pagination={false}
            size="small"
          />
        </Spin>
      </Card>

      {/* 最近文件 */}
      <Card
        title="最近文件"
        extra={<Button type="link" onClick={() => navigate('/admin/files')}>查看全部</Button>}
        style={{ marginBottom: 16 }}
      >
        <Spin spinning={loadingFiles}>
          <Table
            columns={fileColumns}
            dataSource={files.map(file => ({ ...file, key: file.id }))}
            pagination={false}
            size="small"
          />
        </Spin>
      </Card>

      {/* 最近提取记录 */}
      <Card
        title="最近提取记录"
        extra={<Button type="link" onClick={() => navigate('/admin/extraction-records')}>查看全部</Button>}
      >
        <Spin spinning={loadingExtractionRecords}>
          <Table
            columns={extractionColumns}
            dataSource={extractionRecords.map(record => ({ ...record, key: record.id }))}
            pagination={false}
            size="small"
          />
        </Spin>
      </Card>
    </div>
  );
};

export default Dashboard; 