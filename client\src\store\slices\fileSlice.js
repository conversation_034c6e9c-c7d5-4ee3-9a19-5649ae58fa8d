import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// 获取文件列表
export const getFiles = createAsyncThunk(
  'file/getFiles',
  async ({ category_id, keyword, page = 1, limit = 9 } = {}, { rejectWithValue }) => {
    try {
      // 获取token但不设置全局请求头
      const token = localStorage.getItem('userToken');
      
      let url = `/api/files?page=${page}&limit=${limit}`;
      if (category_id) url += `&category_id=${category_id}`;
      if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;

      const response = await axios.get(url, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取文件列表失败');
    }
  }
);

// 获取文件详情
export const getFileDetails = createAsyncThunk(
  'file/getFileDetails',
  async (fileId, { rejectWithValue }) => {
    try {
      // 获取token但不设置全局请求头
      const token = localStorage.getItem('userToken');
      
      const response = await axios.get(`/api/files/${fileId}`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      
      // 检查文件是否存在
      const file = response.data.data.file;
      if (!file) {
        return rejectWithValue('文件不存在或已被删除');
      }
      
      // 处理可能为空的字段
      if (file.file_size === null || file.file_size === undefined) {
        file.file_size = 0;
      }
      
      return file;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取文件详情失败');
    }
  }
);

// 提取文件
export const extractFile = createAsyncThunk(
  'file/extractFile',
  async (fileId, { rejectWithValue }) => {
    try {
      // 获取token但不设置全局请求头
      const token = localStorage.getItem('userToken');
      
      const response = await axios.post(`/api/files/extract/${fileId}`, {}, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      
      // 打印响应数据，用于调试
      console.log('提取文件响应数据:', response.data);
      
      // 确保下载URL使用正确的API路径
      const downloadData = response.data.data;
      if (downloadData && downloadData.downloadUrl) {
        // 确保URL以/api开头
        if (!downloadData.downloadUrl.startsWith('/api/')) {
          downloadData.downloadUrl = `/api${downloadData.downloadUrl}`;
        }
      }

      // 提取成功后直接获取文件详情
      const fileResponse = await axios.get(`/api/files/${fileId}`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      
      console.log('重新获取文件详情:', fileResponse.data);
      
      // 将文件信息添加到返回数据中
      downloadData.file = fileResponse.data.data.file;
      
      return downloadData;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '文件提取失败');
    }
  }
);

const fileSlice = createSlice({
  name: 'file',
  initialState: {
    files: [],
    total: 0,
    page: 1,
    limit: 9,
    currentFile: null,
    downloadUrl: null,
    loading: false,
    loadingDetails: false,
    extracting: false,
    error: null
  },
  reducers: {
    clearFileError: (state) => {
      state.error = null;
    },
    clearCurrentFile: (state) => {
      state.currentFile = null;
      state.downloadUrl = null;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // 获取文件列表
      .addCase(getFiles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getFiles.fulfilled, (state, action) => {
        state.files = action.payload.files;
        state.total = action.payload.total;
        state.page = action.payload.page;
        state.limit = action.payload.limit;
        state.loading = false;
      })
      .addCase(getFiles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // 获取文件详情
      .addCase(getFileDetails.pending, (state) => {
        state.loadingDetails = true;
        state.error = null;
      })
      .addCase(getFileDetails.fulfilled, (state, action) => {
        state.currentFile = action.payload;
        state.loadingDetails = false;
      })
      .addCase(getFileDetails.rejected, (state, action) => {
        state.loadingDetails = false;
        state.error = action.payload;
      })
      
      // 提取文件
      .addCase(extractFile.pending, (state) => {
        state.extracting = true;
        state.error = null;
      })
      .addCase(extractFile.fulfilled, (state, action) => {
        state.downloadUrl = action.payload.downloadUrl;
        // 打印调试信息
        console.log('提取文件成功:', action.payload);
        // 更新文件信息
        if (action.payload.file) {
          console.log('更新文件信息:', action.payload.file);
          state.currentFile = action.payload.file;
        }
        state.extracting = false;
      })
      .addCase(extractFile.rejected, (state, action) => {
        state.extracting = false;
        state.error = action.payload;
      });
  }
});

export const { clearFileError, clearCurrentFile, setPage } = fileSlice.actions;
export default fileSlice.reducer; 