const { sequelize, Op } = require('sequelize');
const File = require('./models/File');

async function checkPreviewImages() {
  try {
    console.log('开始检查简介图片数据...');

    // 查询所有有简介图片的文件
    const files = await File.findAll({
      where: {
        preview_images: {
          [Op.ne]: null
        }
      },
      attributes: ['id', 'file_name', 'preview_images'],
      raw: true
    });

    console.log(`找到 ${files.length} 个有简介图片的文件:`);
    
    files.forEach(file => {
      console.log(`\n文件ID: ${file.id}`);
      console.log(`文件名: ${file.file_name}`);
      console.log(`简介图片JSON: ${file.preview_images}`);
      
      try {
        const parsed = JSON.parse(file.preview_images);
        console.log(`解析后的图片路径:`, parsed);
      } catch (error) {
        console.log(`JSON解析失败: ${error.message}`);
      }
    });

    process.exit(0);
  } catch (error) {
    console.error('检查简介图片数据失败:', error);
    process.exit(1);
  }
}

// 执行检查
checkPreviewImages();
