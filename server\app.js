require('dotenv').config();
const express = require('express');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
// const rateLimit = require('express-rate-limit'); // 注释掉限流模块
const fs = require('fs');

// 导入文件完整性验证工具
const { validateFileIntegrity } = require('./utils/fileIntegrityValidator');
const hashesPath = path.join(__dirname, './utils/hashValues.json');
let storedHashes = {};

// 检查哈希值文件是否存在
if (fs.existsSync(hashesPath)) {
  try {
    storedHashes = JSON.parse(fs.readFileSync(hashesPath, 'utf8'));
  } catch (error) {
    console.error('读取哈希值文件失败:', error);
    process.exit(1); // 退出进程
  }
} else {
  console.error('哈希值文件不存在，请先运行初始化脚本');
  process.exit(1); // 退出进程
}

// 验证文件完整性
const basePath = path.resolve(__dirname, '..');
const integrityValid = validateFileIntegrity(basePath, storedHashes);
if (!integrityValid) {
  console.error('文件完整性验证失败，系统无法启动');
  console.error('检测到关键文件被修改，请恢复原始文件');
  process.exit(1); // 退出进程
}

// 导入授权验证系统
const { verifySystemAuthorization } = require('./utils/authorizationLauncher');

// 导入数据库配置和模型
const { testConnection } = require('./config/database');
require('./models');

// 导入定时任务调度器
const { initScheduler } = require('./utils/scheduler');

// 创建Express应用程序
const app = express();

// 基本中间件
app.use(helmet({
  contentSecurityPolicy: false, // 禁用CSP可能导致的HTTPS跳转
  referrerPolicy: { policy: 'no-referrer-when-downgrade' }, // 降级时不发送referrer
})); // 安全HTTP头
app.use(cors()); // 跨域资源共享
app.use(express.json()); // JSON解析
app.use(express.urlencoded({ extended: true })); // URL编码解析

// 明确配置只使用HTTP协议
app.use((req, res, next) => {
  res.removeHeader('Strict-Transport-Security');
  next();
});

// 设置上传路径
const uploadPath = path.join(__dirname, 'uploads');
console.log('上传文件路径设置为:', uploadPath);

// 创建上传目录
if (!fs.existsSync(uploadPath)) {
  fs.mkdirSync(uploadPath, { recursive: true });
}

/* API限流配置已被移除
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每IP限制100次请求
  standardHeaders: true,
  legacyHeaders: false,
  message: { success: false, message: '请求过于频繁，请稍后再试' }
});

// 登录接口限流（更宽松）
const loginLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 20, // 每IP限制20次请求
  standardHeaders: true,
  legacyHeaders: false,
  message: { success: false, message: '登录尝试次数过多，请5分钟后再试' }
});

// 应用API限流中间件
app.use('/api/auth/admin/login', loginLimiter); // 管理员登录接口使用专门的限流规则
app.use('/api/auth/verify', loginLimiter); // 用户验证卡密接口使用专门的限流规则
app.use('/api', apiLimiter); // 其他API使用通用限流规则
*/

// 静态文件服务
app.use('/static', express.static(path.join(__dirname, 'public')));

// API路由
const routes = require('./routes');
app.use('/api', routes);

// 前端静态文件服务（生产环境）
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, '../client/build', 'index.html'));
  });
}

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ success: false, message: '服务器内部错误' });
});

// 启动服务器
const PORT = process.env.PORT || 5000;

// 验证授权后启动应用
(async () => {
  try {
    console.log('启动授权验证系统...');
    
    // 验证系统授权
    const authorized = await verifySystemAuthorization();
    
    if (!authorized) {
      console.error('系统授权验证失败，无法启动应用');
      process.exit(1); // 退出进程
    }
    
    // 授权通过，启动服务器
app.listen(PORT, async () => {
  console.log(`服务器运行在端口 ${PORT}`);
  
  // 测试数据库连接
  await testConnection();
  
  // 初始化定时任务调度器
  initScheduler();
});
  } catch (error) {
    console.error('启动过程中发生错误:', error);
    process.exit(1); // 退出进程
  }
})();

module.exports = app; 