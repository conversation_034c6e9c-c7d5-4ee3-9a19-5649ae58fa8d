const { sequelize } = require('./config/database');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const mysql = require('mysql2/promise');

// MySQL连接信息
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  multipleStatements: true // 允许多条SQL语句
};

async function initDb() {
  console.log('开始初始化数据库...');

  try {
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    
    console.log('连接MySQL成功，开始创建数据库...');
    
    // 创建数据库
    await connection.query('DROP DATABASE IF EXISTS zizhutihuoxitong');
    await connection.query('CREATE DATABASE zizhutihuoxitong CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    
    console.log('数据库创建成功，开始导入数据表...');
    
    // 读取SQL文件内容
    const sqlPath = path.join(__dirname, '../database/schema.sql');
    const sqlScript = fs.readFileSync(sqlPath, 'utf8');
    
    // 切换到创建的数据库
    await connection.query('USE zizhutihuoxitong');
    
    // 执行SQL脚本
    await connection.query(sqlScript);
    
    console.log('数据表导入成功，数据库初始化完成！');
    
    // 关闭连接
    await connection.end();
    
    // 退出进程
    process.exit(0);
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

// 执行初始化
initDb(); 