const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * 生成随机卡密
 * @param {Number} length - 卡密长度（默认16位）
 * @returns {String} - 随机卡密
 */
const generateRandomKey = (length = 16) => {
  // 生成随机字节
  const randomBytes = crypto.randomBytes(Math.ceil(length / 2));
  // 转换为16进制字符串
  return randomBytes.toString('hex').slice(0, length).toUpperCase();
};

/**
 * 批量生成卡密
 * @param {Number} count - 要生成的卡密数量
 * @param {Number} length - 每个卡密的长度
 * @returns {Array<String>} - 卡密数组
 */
const batchGenerateKeys = (count = 1, length = 16) => {
  const keys = [];
  for (let i = 0; i < count; i++) {
    keys.push(generateRandomKey(length));
  }
  return keys;
};

/**
 * 生成带前缀的卡密
 * @param {String} prefix - 卡密前缀
 * @param {Number} count - 要生成的卡密数量
 * @returns {Array<String>} - 带前缀的卡密数组
 */
const generateKeysWithPrefix = (prefix = '', count = 1) => {
  const keys = [];
  for (let i = 0; i < count; i++) {
    // 使用UUID的一部分生成卡密
    const uuid = uuidv4().replace(/-/g, '').substring(0, 16).toUpperCase();
    keys.push(`${prefix}${uuid}`);
  }
  return keys;
};

module.exports = {
  generateRandomKey,
  batchGenerateKeys,
  generateKeysWithPrefix
}; 