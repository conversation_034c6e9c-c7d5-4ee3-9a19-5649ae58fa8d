const cron = require('node-cron');
const { cleanupExpiredCardKeys } = require('./cleanupTasks');

/**
 * 初始化定时任务调度器
 */
const initScheduler = () => {
  // 每天凌晨3点执行清理过期卡密任务
  // 格式: 秒 分 时 日 月 星期
  cron.schedule('0 0 3 * * *', async () => {
    console.log('开始执行定时清理过期卡密任务...');
    try {
      const result = await cleanupExpiredCardKeys();
      if (result.success) {
        console.log(`定时任务完成: ${result.message}`);
      } else {
        console.error(`定时任务失败: ${result.message}`);
      }
    } catch (error) {
      console.error('执行定时清理过期卡密任务时发生错误:', error);
    }
  });
  
  console.log('定时任务调度器已初始化');
};

module.exports = {
  initScheduler
}; 