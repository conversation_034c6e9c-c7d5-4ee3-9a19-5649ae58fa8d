const { verifyToken } = require('../utils/jwt');

/**
 * 验证用户JWT令牌中间件
 */
const verifyUserToken = (req, res, next) => {
  // 从请求头或查询参数获取令牌
  const token = req.headers.authorization?.split(' ')[1] || req.query.token;
  
  if (!token) {
    return res.status(401).json({ success: false, message: '未提供访问令牌' });
  }

  // 验证令牌
  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(401).json({ success: false, message: '无效的令牌或令牌已过期' });
  }

  // 检查令牌类型
  if (decoded.type !== 'user') {
    return res.status(403).json({ success: false, message: '权限不足' });
  }

  // 将解码的数据附加到请求对象
  req.user = decoded;
  next();
};

/**
 * 验证管理员JWT令牌中间件
 */
const verifyAdminToken = (req, res, next) => {
  // 从请求头或查询参数获取令牌
  const token = req.headers.authorization?.split(' ')[1] || req.query.token;
  
  if (!token) {
    return res.status(401).json({ success: false, message: '未提供访问令牌' });
  }

  // 验证令牌
  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(401).json({ success: false, message: '无效的令牌或令牌已过期' });
  }

  // 检查令牌类型
  if (decoded.type !== 'admin') {
    return res.status(403).json({ success: false, message: '需要管理员权限' });
  }

  // 将解码的数据附加到请求对象
  req.admin = decoded;
  next();
};

module.exports = {
  verifyUserToken,
  verifyAdminToken,
  authMiddleware: verifyUserToken
}; 