# 文件完整性验证系统说明

## 概述

本系统实现了基于SHA-256算法的文件完整性验证机制，能有效防止授权验证相关代码被修改。此机制确保任何对关键文件的修改（包括代码、注释、空格或换行符）都会导致系统无法启动，从而保护授权验证功能的完整性。

## 工作原理

1. 系统在初始化时，会计算所有关键授权文件的SHA-256哈希值，并保存至配置文件
2. 每次启动系统时，会重新计算这些文件的哈希值，并与保存的值进行比对
3. 如果检测到任何文件的哈希值不匹配，系统会立即停止启动，并显示错误信息

## 受保护的文件

以下文件受到完整性保护，任何修改都将导致启动失败：

- 授权控制器: server/controllers/authController.js
- 卡密控制器: server/controllers/cardKeyController.js  
- 文件控制器: server/controllers/fileController.js
- 卡密模型: server/models/CardKey.js
- JWT工具: server/utils/jwt.js
- 直接授权工具: server/utils/directAuthorization.js
- 授权启动器: server/utils/authorizationLauncher.js
- 认证中间件: server/middlewares/auth.js
- 日志中间件: server/middlewares/logger.js
- 授权路由: server/routes/authRoutes.js
- 卡密生成器: server/utils/keyGenerator.js
- 客户端授权状态管理: client/src/store/slices/authSlice.js

## 使用说明

### 初始化文件完整性验证系统

首次部署系统或更新代码后，必须运行初始化脚本：

```
initialize-hashes.bat
```

此脚本会计算所有关键文件的哈希值并保存，作为后续验证的基准。

### 启动系统

使用提供的启动脚本启动系统：

```
start-system.bat
```

启动脚本会自动验证文件完整性，只有在所有文件未被修改的情况下才会继续启动系统。

### 错误处理

如果系统启动时显示文件完整性验证失败的错误信息，请检查以下几点：

1. 是否修改了受保护的文件？如果是，请恢复原始文件
2. 是否在更新系统后忘记重新初始化哈希值？如果是，请运行 initialize-hashes.bat
3. 文件是否意外损坏？请重新部署系统

## 注意事项

1. 更新系统后务必重新运行初始化脚本，否则系统将无法启动
2. 不要尝试修改受保护的文件，这会导致系统启动失败
3. 不要删除或修改哈希值配置文件(server/utils/hashValues.json)
4. 如需修改系统功能，请联系系统提供商 