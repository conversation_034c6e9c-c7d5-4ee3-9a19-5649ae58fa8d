import React, { useEffect, useState } from 'react';
import { List, Card, Button, Input, Select, Empty, Spin, Pagination, Typography, Space, Tag } from 'antd';
import { SearchOutlined, FileOutlined, FileTextOutlined, FileImageOutlined, FilePdfOutlined, FileZipOutlined, FileUnknownOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getFiles, setPage, clearFileError } from '../store/slices/fileSlice';
import { getCategories } from '../store/slices/categorySlice';
import axios from 'axios';

const { Title, Text } = Typography;
const { Option } = Select;

// 用户端图片组件，支持身份验证
const UserAuthenticatedImage = ({ src, ...props }) => {
  const [imageSrc, setImageSrc] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      try {
        // 检查是否是外部链接（如闲鱼图片）
        if (src.startsWith('http://') || src.startsWith('https://')) {
          // 外部链接直接使用
          setImageSrc(src);
          setLoading(false);
          return;
        }

        // 本地图片通过API加载
        const userToken = localStorage.getItem('userToken');
        const response = await axios.get(src, {
          headers: userToken ? {
            'Authorization': `Bearer ${userToken}`
          } : {},
          responseType: 'blob'
        });

        const imageUrl = URL.createObjectURL(response.data);
        setImageSrc(imageUrl);
        setLoading(false);
      } catch (error) {
        console.error('加载图片失败:', error);
        setLoading(false);
      }
    };

    if (src) {
      loadImage();
    }

    return () => {
      if (imageSrc && !imageSrc.startsWith('http')) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [src]);

  if (loading) {
    return <div style={{ width: props.width || 60, height: props.height || 60, background: '#f0f0f0', borderRadius: 4 }} />;
  }

  if (!imageSrc) {
    return null;
  }

  return <img src={imageSrc} {...props} alt="" />;
};

// 文件图标映射
const getFileIcon = (fileType) => {
  switch (fileType?.toLowerCase()) {
    case 'pdf':
      return <FilePdfOutlined style={{ fontSize: 36, color: '#ff4d4f' }} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
      return <FileImageOutlined style={{ fontSize: 36, color: '#52c41a' }} />;
    case 'doc':
    case 'docx':
    case 'txt':
      return <FileTextOutlined style={{ fontSize: 36, color: '#1890ff' }} />;
    case 'zip':
    case 'rar':
    case '7z':
      return <FileZipOutlined style={{ fontSize: 36, color: '#faad14' }} />;
    default:
      return <FileOutlined style={{ fontSize: 36, color: '#722ed1' }} />;
  }
};

// 格式化文件大小
const formatFileSize = (sizeInKB) => {
  // 如果文件大小为null或0（网盘链接类资料），不显示大小
  if (sizeInKB === null || sizeInKB === 0) {
    return '';
  }
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} MB`;
  }
};

const FileList = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { files, total, page, limit, loading, error } = useSelector((state) => state.file);
  const { categories, loading: loadingCategories } = useSelector((state) => state.category);

  // 加载分类和文件列表
  useEffect(() => {
    const token = localStorage.getItem('userToken');
    if (token) {
      // 确保设置请求头
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }
    
    dispatch(getCategories());
    dispatch(getFiles({ page, limit }));
  }, [dispatch, page, limit]);

  // 处理搜索
  const handleSearch = () => {
    dispatch(getFiles({
      page: 1,
      limit,
      keyword: searchKeyword,
      category_id: selectedCategory
    }));
  };

  // 处理分页变化
  const handlePageChange = (newPage) => {
    dispatch(setPage(newPage));
    dispatch(getFiles({
      page: newPage,
      limit,
      keyword: searchKeyword,
      category_id: selectedCategory
    }));
  };

  // 查看文件详情
  const viewFileDetails = (id) => {
    navigate(`/files/${id}`);
  };

  return (
    <div>
      <Title level={2}>文件资料列表</Title>
      <Text type="secondary">
        请选择您要查看和提取的文件资料
      </Text>

      {/* 搜索和筛选 */}
      <div style={{ marginTop: 20, marginBottom: 20, display: 'flex', gap: 16 }}>
        <Input
          placeholder="搜索文件名称"
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          style={{ width: 250 }}
          prefix={<SearchOutlined />}
          onPressEnter={handleSearch}
        />
        
        <Select
          placeholder="选择分类"
          style={{ width: 200 }}
          allowClear
          loading={loadingCategories}
          onChange={(value) => setSelectedCategory(value)}
        >
          {categories.map((category) => (
            <Option key={category.id} value={category.id}>
              {category.name}
            </Option>
          ))}
        </Select>
        
        <Button type="primary" onClick={handleSearch}>
          搜索
        </Button>
      </div>

      {/* 文件列表 */}
      <Spin spinning={loading}>
        {files.length > 0 ? (
          <List
            grid={{ gutter: 16, column: 3, xs: 1, sm: 2, md: 3 }}
            dataSource={files}
            renderItem={(file) => (
              <List.Item>
                <Card
                  hoverable
                  style={{ height: '100%', overflow: 'hidden' }}
                  onClick={() => viewFileDetails(file.id)}
                >
                  <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                    <div style={{ marginRight: 16 }}>
                      {/* 简介图片缩略图 */}
                      {file.preview_images_parsed && file.preview_images_parsed.length > 0 ? (
                        <UserAuthenticatedImage
                          width={60}
                          height={60}
                          src={file.preview_images_parsed[0].startsWith('http') ? file.preview_images_parsed[0] : `/api/files/preview/${file.preview_images_parsed[0]}`}
                          style={{ objectFit: 'cover', borderRadius: 4, width: 60, height: 60 }}
                        />
                      ) : (
                        getFileIcon(file.file_type)
                      )}
                    </div>
                    <div style={{ flex: 1, overflow: 'hidden' }}>
                      <div style={{ width: '100%', overflow: 'hidden' }}>
                        <Text strong title={file.file_name} style={{ 
                          fontSize: 16,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: 'block'
                        }}>
                          {file.file_name}
                        </Text>
                      </div>
                      
                      <div style={{ marginTop: 8 }}>
                        <Space size={[0, 8]} wrap>
                          <Tag color="blue">{file.file_type.toUpperCase()}</Tag>
                          {file.file_size !== null && file.file_size > 0 && 
                          <Tag color="cyan">{formatFileSize(file.file_size)}</Tag>
                          }
                          {file.Category && <Tag color="purple">{file.Category.name}</Tag>}
                        </Space>
                      </div>
                      
                      <div style={{ marginTop: 16, width: '100%', overflow: 'hidden' }}>
                        <Text 
                          type="secondary" 
                          title={file.description || '暂无描述信息'}
                          style={{
                            width: '100%',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: 'block'
                          }}
                        >
                          {file.description || '暂无描述信息'}
                        </Text>
                      </div>
                      
                      <div style={{ marginTop: 16, textAlign: 'right' }}>
                        <Button type="primary" size="small">查看详情</Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        ) : (
          <Empty
            description={
              <span>
                {error ? `加载失败: ${error}` : '暂无符合条件的文件资料'}
              </span>
            }
          />
        )}

        {/* 分页 */}
        {total > 0 && (
          <div style={{ marginTop: 24, textAlign: 'right' }}>
            <Pagination
              current={page}
              pageSize={limit}
              total={total}
              onChange={handlePageChange}
              showTotal={(total) => `共 ${total} 条记录`}
            />
          </div>
        )}
      </Spin>
    </div>
  );
};

export default FileList; 