const express = require('express');
const router = express.Router();
const authRoutes = require('./authRoutes');
const fileRoutes = require('./fileRoutes');
const adminRoutes = require('./adminRoutes');
const categoryController = require('../controllers/categoryController');
const fileController = require('../controllers/fileController');

// API根路由
router.get('/', (req, res) => {
  res.json({ message: '自助提取系统API服务正常运行' });
});

// 公开的分类列表接口（不需要验证）
router.get('/categories', categoryController.getCategories);

// 单独的文件下载路由（放在前面以避免与其他路由冲突）
router.get('/download/:fileId', fileController.downloadFile);

// 子路由
router.use('/auth', authRoutes);
router.use('/files', fileRoutes);
router.use('/admin', adminRoutes);

module.exports = router; 