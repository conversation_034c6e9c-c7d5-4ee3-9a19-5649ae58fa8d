{"name": "<PERSON><PERSON><PERSON><PERSON>huoxitong-server", "version": "1.0.0", "description": "自助提取系统后端API", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["api", "express", "mysql", "sequelize"], "author": "", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.52.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.6.5", "node-cron": "^3.0.3", "sequelize": "^6.35.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}