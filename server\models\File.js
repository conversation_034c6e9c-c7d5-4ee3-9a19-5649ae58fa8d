const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const Category = require('./Category');

const File = sequelize.define('File', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  file_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '文件名称'
  },
  file_path: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '文件路径'
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '文件大小(KB)'
  },
  file_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '文件类型'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '文件描述'
  },
  important_text: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '网盘链接'
  },
  preview_images: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '简介图片路径JSON数组'
  },
  upload_time: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '上传时间'
  },
  download_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '下载次数'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态(1:可用,0:不可用)'
  },
  category_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '分类ID',
    references: {
      model: Category,
      key: 'id'
    }
  }
}, {
  tableName: 'files',
  timestamps: false
});

// 建立文件和分类的关联关系
File.belongsTo(Category, { foreignKey: 'category_id' });
Category.hasMany(File, { foreignKey: 'category_id' });

module.exports = File; 