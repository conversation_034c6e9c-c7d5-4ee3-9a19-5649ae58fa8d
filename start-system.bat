@echo off
chcp 65001 > nul
setlocal EnableDelayedExpansion

echo ==================================================
echo          自助提货系统启动器 v1.0
echo ==================================================
echo.

:: 保存当前目录路径
set "PROJECT_ROOT=%CD%"
echo 项目根目录: %PROJECT_ROOT%

echo 正在检查环境...

:: 检查Node.js是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js! 请安装Node.js后再运行此脚本。
    echo 您可以从 https://nodejs.org 下载并安装Node.js。
    pause
    exit /b 1
)

echo [成功] Node.js 已安装

:: 检查项目文件夹结构是否正确
if not exist "server" (
    echo [错误] 未找到服务器目录! 请确保在正确的项目根目录下运行此脚本。
    pause
    exit /b 1
)

if not exist "client" (
    echo [错误] 未找到客户端目录! 请确保在正确的项目根目录下运行此脚本。
    pause
    exit /b 1
)

echo [成功] 项目文件结构验证通过

:: 验证文件完整性
echo.
echo 正在验证文件完整性...
node "%PROJECT_ROOT%\server\utils\verifyIntegrity.js"
if %errorlevel% neq 0 (
    echo.
    echo [错误] 文件完整性验证失败！
    echo 系统检测到关键文件被修改，无法启动。
    echo 请恢复原始文件后再试。
    echo.
    pause
    exit /b 1
)

echo [成功] 文件完整性验证通过

:: 设置窗口标题
title 自助提货系统启动器

echo.
echo 正在检查依赖项...

:: 检查并安装后端依赖
echo 检查后端依赖项...
cd /d "%PROJECT_ROOT%\server"
if not exist "node_modules" (
    echo [提示] 后端依赖项未安装，正在安装...
    npm install
    if %errorlevel% neq 0 (
        echo [错误] 后端依赖项安装失败！
        pause
        exit /b 1
    )
    echo [成功] 后端依赖项安装完成
) else (
    echo [成功] 后端依赖项已存在
)

:: 检查并安装前端依赖
echo 检查前端依赖项...
cd /d "%PROJECT_ROOT%\client"
if not exist "node_modules" (
    echo [提示] 前端依赖项未安装，正在安装...
    npm install
    if %errorlevel% neq 0 (
        echo [错误] 前端依赖项安装失败！
        pause
        exit /b 1
    )
    echo [成功] 前端依赖项安装完成
) else (
    echo [成功] 前端依赖项已存在
)

echo.
echo 所有依赖项检查完成，即将启动系统...
echo.

:: 回到项目根目录
cd /d "%PROJECT_ROOT%"

echo 正在启动后端服务...
echo.

:: 启动后端服务，后端服务会验证授权
cd /d "%PROJECT_ROOT%\server"
start "自助提货系统-后端服务" cmd /c "npm start"

:: 等待几秒钟让后端启动并完成授权验证
echo 等待后端服务启动...
echo 请在弹出的控制台窗口中输入授权码...
timeout /t 8 /nobreak > nul

:: 检查后端是否启动成功
:: 使用PowerShell代替curl来检查API服务是否响应
powershell -Command "try { $null = Invoke-WebRequest -Uri 'http://localhost:5000/api' -UseBasicParsing -ErrorAction Stop; exit 0 } catch { exit 1 }"
if %errorlevel% neq 0 (
    echo [警告] 后端服务可能未成功启动，请检查授权是否成功。
    echo.
    echo 等待额外的时间后重试...
    timeout /t 5 /nobreak > nul
    
    :: 再次尝试检查后端
    powershell -Command "try { $null = Invoke-WebRequest -Uri 'http://localhost:5000/api' -UseBasicParsing -ErrorAction Stop; exit 0 } catch { exit 1 }"
    if %errorlevel% neq 0 (
        echo [错误] 后端服务启动失败，请检查授权是否通过。
        pause
        exit /b 1
    )
)

echo [成功] 后端服务已启动

:: 启动前端开发服务器
echo.
echo 正在启动前端服务...

:: 创建一个临时启动脚本，确保在正确目录中执行
echo @echo off > "%PROJECT_ROOT%\start-frontend.bat"
echo cd /d "%PROJECT_ROOT%\client" >> "%PROJECT_ROOT%\start-frontend.bat"
echo echo 正在启动前端服务... >> "%PROJECT_ROOT%\start-frontend.bat"
echo npm start >> "%PROJECT_ROOT%\start-frontend.bat"
echo pause >> "%PROJECT_ROOT%\start-frontend.bat"

:: 启动前端服务
start "自助提货系统-前端服务" "%PROJECT_ROOT%\start-frontend.bat"

:: 回到项目根目录
cd /d "%PROJECT_ROOT%"

echo.
echo ==================================================
echo      系统已启动! 请在浏览器中访问以下地址:
echo      前端界面: http://localhost:3000
echo      后端API: http://localhost:5000/api
echo ==================================================
echo.
echo 提示: 关闭此窗口将不会停止服务，需要手动关闭服务窗口

:: 保持脚本运行状态
pause
exit /b 0 