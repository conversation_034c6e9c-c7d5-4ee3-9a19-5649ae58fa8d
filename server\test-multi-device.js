const { sequelize } = require('./config/database');
const { CardKey } = require('./models');
const { v4: uuidv4 } = require('uuid');

async function testMultiDeviceCardKey() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 创建一个多设备卡密
    const multiDeviceKey = await CardKey.create({
      key_code: `TEST_${uuidv4().substring(0, 8).toUpperCase()}`,
      status: true,
      key_type: 'multi',
      max_devices: 3,
      expire_time: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
    });

    console.log('创建多设备卡密成功:', multiDeviceKey.toJSON());

    // 模拟设备1使用卡密
    const device1 = '1111aaaa2222bbbb';
    console.log('\n模拟设备1使用卡密');
    await simulateDeviceUse(multiDeviceKey.id, device1);

    // 模拟设备2使用卡密
    const device2 = '3333cccc4444dddd';
    console.log('\n模拟设备2使用卡密');
    await simulateDeviceUse(multiDeviceKey.id, device2);

    // 模拟设备1再次使用卡密（应被拒绝）
    console.log('\n模拟设备1再次使用卡密（应被拒绝）');
    await simulateDeviceUse(multiDeviceKey.id, device1);

    // 模拟设备3使用卡密
    const device3 = '5555eeee6666ffff';
    console.log('\n模拟设备3使用卡密');
    await simulateDeviceUse(multiDeviceKey.id, device3);

    // 模拟设备4使用卡密（应被拒绝，已达到最大设备数）
    const device4 = '7777gggg8888hhhh';
    console.log('\n模拟设备4使用卡密（应被拒绝，已达到最大设备数）');
    await simulateDeviceUse(multiDeviceKey.id, device4);

    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

async function simulateDeviceUse(cardKeyId, deviceFingerprint) {
  try {
    // 查询卡密
    const cardKey = await CardKey.findByPk(cardKeyId);
    if (!cardKey) {
      console.log('卡密不存在');
      return;
    }

    console.log(`当前卡密状态: is_used=${cardKey.is_used}, used_devices=${cardKey.used_devices || 'null'}`);

    // 解析已使用设备列表
    let usedDevices = [];
    if (cardKey.used_devices) {
      try {
        usedDevices = JSON.parse(cardKey.used_devices);
      } catch (err) {
        console.error('解析已使用设备列表错误:', err);
      }
    }

    // 检查当前设备是否已经使用过该卡密
    if (usedDevices.includes(deviceFingerprint)) {
      console.log('该设备已使用过此卡密');
      return;
    }

    // 检查是否超出最大设备数
    if (usedDevices.length >= cardKey.max_devices) {
      console.log('卡密已达到最大使用设备数');
      return;
    }

    // 将当前设备添加到已使用设备列表
    usedDevices.push(deviceFingerprint);
    cardKey.used_devices = JSON.stringify(usedDevices);
    
    // 如果达到最大使用设备数，则标记为已使用
    if (usedDevices.length >= cardKey.max_devices) {
      cardKey.is_used = true;
    }
    
    // 保存更新
    await cardKey.save();
    console.log(`设备 ${deviceFingerprint} 成功使用卡密`);
    console.log(`更新后卡密状态: is_used=${cardKey.is_used}, used_devices=${cardKey.used_devices}`);
  } catch (error) {
    console.error('模拟使用卡密失败:', error);
  }
}

testMultiDeviceCardKey(); 